% 侵入带几何建模位置详细分析
% 解析main.m中侵入带在图中的精确位置定义

clear; clc;

fprintf('=== 侵入带几何建模位置分析 ===\n\n');

%% 1. 基础网格参数（从main.m提取）
% 网格参数
vp1 = 1500;        % 井孔纵波速度 [m/s]
f0 = 10*10^3;      % 主频率 [Hz]
la1 = vp1/f0;      % 波长计算 [m]
dx = la1/10;       % X方向空间步长 [m]
dz = la1/10;       % Z方向空间步长 [m]
pml = 50;          % PML边界层厚度
nx = 2*pml+200;    % X方向总网格数 = 300
nz = 2*pml+1200;   % Z方向总网格数 = 1300

% 井孔参数
cal = 0.1;         % 井径 [m]
med_x = fix(nx/2) - fix(nx/4);  % 井轴在X方向的位置
l_cal = ceil(cal/dx)/1;         % 井径对应的网格点数

% 侵入带参数
Formation_D = 1.0;             % 侵入带径向厚度 [m]
Formation_DIter = ceil(Formation_D/dz);  % 侵入带径向网格数
Formation_H = 1.0;             % 侵入带轴向高度 [m]
Formation_HIter = ceil(Formation_H/dz);  % 侵入带轴向网格数

fprintf('1. 基础网格参数:\n');
fprintf('==================\n');
fprintf('网格尺寸: nx=%d, nz=%d\n', nx, nz);
fprintf('网格步长: dx=dz=%.4f m\n', dx);
fprintf('物理尺寸: %.2f × %.2f m\n', nx*dx, nz*dz);
fprintf('PML厚度: %d 网格点\n', pml);

%% 2. 井孔位置计算详解
fprintf('\n2. 井孔位置计算:\n');
fprintf('================\n');
fprintf('井轴X坐标计算:\n');
fprintf('  med_x = fix(nx/2) - fix(nx/4)\n');
fprintf('  med_x = fix(%d/2) - fix(%d/4)\n', nx, nx);
fprintf('  med_x = fix(%d) - fix(%d)\n', nx/2, nx/4);
fprintf('  med_x = %d - %d = %d\n', fix(nx/2), fix(nx/4), med_x);
fprintf('  井轴物理位置: %.3f m\n', med_x * dx);

fprintf('\n井径网格点数计算:\n');
fprintf('  l_cal = ceil(cal/dx) = ceil(%.3f/%.4f) = ceil(%.2f) = %d\n', ...
    cal, dx, cal/dx, l_cal);
fprintf('  井径物理尺寸: %.3f m\n', l_cal * dx);

fprintf('\n井孔边界位置:\n');
fprintf('  左边界: 第%d列 (%.3f m)\n', med_x-l_cal, (med_x-l_cal)*dx);
fprintf('  右边界: 第%d列 (%.3f m)\n', med_x+l_cal, (med_x+l_cal)*dx);
fprintf('  井孔范围: 第%d列 ~ 第%d列\n', med_x-l_cal, med_x+l_cal);

%% 3. 侵入带位置计算详解
fprintf('\n3. 侵入带位置计算:\n');
fprintf('==================\n');

% 径向位置计算
invasion_start_x = med_x + l_cal + 1;
invasion_end_x = med_x + l_cal + Formation_DIter;

fprintf('径向位置计算:\n');
fprintf('  Formation_DIter = ceil(Formation_D/dz)\n');
fprintf('  Formation_DIter = ceil(%.1f/%.4f) = ceil(%.2f) = %d\n', ...
    Formation_D, dz, Formation_D/dz, Formation_DIter);

fprintf('\n径向边界:\n');
fprintf('  侵入带开始: 第%d列 (%.3f m)\n', invasion_start_x, invasion_start_x*dx);
fprintf('  侵入带结束: 第%d列 (%.3f m)\n', invasion_end_x, invasion_end_x*dx);
fprintf('  侵入带厚度: %d网格点 (%.3f m)\n', Formation_DIter, Formation_DIter*dx);

% 轴向位置计算
center_z = nz/2;
invasion_start_z = nz/2 - ceil(Formation_HIter/2);
invasion_end_z = nz/2 + ceil(Formation_HIter/2);

fprintf('\n轴向位置计算:\n');
fprintf('  Formation_HIter = ceil(Formation_H/dz)\n');
fprintf('  Formation_HIter = ceil(%.1f/%.4f) = ceil(%.2f) = %d\n', ...
    Formation_H, dz, Formation_H/dz, Formation_HIter);

fprintf('\n轴向边界:\n');
fprintf('  模型中心: 第%.1f行 (%.3f m)\n', center_z, center_z*dz);
fprintf('  侵入带上边界: 第%d行 (%.3f m)\n', invasion_start_z, invasion_start_z*dz);
fprintf('  侵入带下边界: 第%d行 (%.3f m)\n', invasion_end_z, invasion_end_z*dz);
fprintf('  侵入带高度: %d网格点 (%.3f m)\n', Formation_HIter, Formation_HIter*dz);

%% 4. 完整的几何结构分析
fprintf('\n4. 完整几何结构:\n');
fprintf('================\n');

% 定义各区域边界
regions = {
    '左侧地层', 1, med_x-l_cal-1;
    '井孔区域', med_x-l_cal, med_x+l_cal;
    '侵入带', invasion_start_x, invasion_end_x;
    '右侧地层', invasion_end_x+1, nx
};

fprintf('X方向区域划分:\n');
fprintf('%-12s %-15s %-15s %-15s\n', '区域名称', '网格范围', '物理位置(m)', '宽度(m)');
fprintf('%-12s %-15s %-15s %-15s\n', repmat('-',1,12), repmat('-',1,15), repmat('-',1,15), repmat('-',1,15));

for i = 1:size(regions, 1)
    name = regions{i, 1};
    start_col = regions{i, 2};
    end_col = regions{i, 3};
    width = (end_col - start_col + 1) * dx;
    start_pos = start_col * dx;
    end_pos = end_col * dx;
    
    fprintf('%-12s 第%d~%d列     %.3f~%.3f     %.3f\n', ...
        name, start_col, end_col, start_pos, end_pos, width);
end

fprintf('\nZ方向区域划分:\n');
fprintf('%-12s %-15s %-15s %-15s\n', '区域名称', '网格范围', '物理位置(m)', '高度(m)');
fprintf('%-12s %-15s %-15s %-15s\n', repmat('-',1,12), repmat('-',1,15), repmat('-',1,15), repmat('-',1,15));
fprintf('%-12s 第%d~%d行     %.3f~%.3f     %.3f\n', ...
    '上部地层', 1, invasion_start_z-1, 0, (invasion_start_z-1)*dz, (invasion_start_z-1)*dz);
fprintf('%-12s 第%d~%d行     %.3f~%.3f     %.3f\n', ...
    '侵入带', invasion_start_z, invasion_end_z, invasion_start_z*dz, invasion_end_z*dz, Formation_HIter*dz);
fprintf('%-12s 第%d~%d行     %.3f~%.3f     %.3f\n', ...
    '下部地层', invasion_end_z+1, nz, (invasion_end_z+1)*dz, nz*dz, (nz-invasion_end_z)*dz);

%% 5. 代码实现对应关系
fprintf('\n5. 代码实现对应关系:\n');
fprintf('====================\n');

fprintf('侵入带赋值的双重循环:\n');
fprintf('for count_i = %d:%d  %% Z方向循环（轴向）\n', invasion_start_z, invasion_end_z);
fprintf('    for count_j = %d:%d  %% X方向循环（径向）\n', invasion_start_x, invasion_end_x);
fprintf('        vp(count_i, count_j) = Vpout;\n');
fprintf('        vs(count_i, count_j) = Vsout;\n');
fprintf('        dens(count_i, count_j) = Denout;\n');
fprintf('    end\n');
fprintf('end\n');

fprintf('\n循环范围解释:\n');
fprintf('• count_i: 控制侵入带的轴向（垂直）范围\n');
fprintf('• count_j: 控制侵入带的径向（水平）范围\n');
fprintf('• 侵入带位于井孔右侧，模型中心附近\n');

%% 6. 可视化几何结构
fprintf('\n6. 生成几何结构可视化:\n');
fprintf('========================\n');

% 创建模型矩阵用于可视化
model = ones(nz, nx);  % 初始化为地层(值=1)

% 井孔区域 (值=2)
model(:, med_x-l_cal:med_x+l_cal) = 2;

% 侵入带区域 (值=3)
model(invasion_start_z:invasion_end_z, invasion_start_x:invasion_end_x) = 3;

% 创建坐标轴
x_coords = (1:nx) * dx;
z_coords = (1:nz) * dz;

% 绘制几何结构
figure('Position', [100, 100, 1200, 800], 'Color', 'w');

% 主图：完整模型
subplot(2, 2, [1, 2]);
imagesc(x_coords, z_coords, model);
colormap([0.8 0.6 0.4; 0.6 0.8 1.0; 1.0 0.6 0.6]);  % 棕色地层，蓝色井孔，红色侵入带
title('侵入带几何模型 - 完整视图', 'FontSize', 14, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
ylabel('Z方向距离 (m)');

% 添加标注
hold on;
% 井轴线
plot([med_x*dx, med_x*dx], [0, nz*dz], 'k--', 'LineWidth', 2);
text(med_x*dx + 0.1, nz*dz*0.9, '井轴', 'FontSize', 12, 'FontWeight', 'bold');

% 井孔边界
plot([(med_x-l_cal)*dx, (med_x-l_cal)*dx], [0, nz*dz], 'b--', 'LineWidth', 1);
plot([(med_x+l_cal)*dx, (med_x+l_cal)*dx], [0, nz*dz], 'b--', 'LineWidth', 1);

% 侵入带边界
rectangle('Position', [invasion_start_x*dx, invasion_start_z*dz, ...
    Formation_DIter*dx, Formation_HIter*dz], ...
    'EdgeColor', 'red', 'LineWidth', 2, 'LineStyle', '-');
text(invasion_start_x*dx + 0.1, invasion_start_z*dz + 0.1, '侵入带', ...
    'Color', 'red', 'FontSize', 12, 'FontWeight', 'bold');

% 局部放大图：井孔附近
subplot(2, 2, 3);
zoom_range_x = max(1, med_x-20):min(nx, med_x+100);
zoom_range_z = max(1, invasion_start_z-50):min(nz, invasion_end_z+50);
imagesc(x_coords(zoom_range_x), z_coords(zoom_range_z), model(zoom_range_z, zoom_range_x));
colormap([0.8 0.6 0.4; 0.6 0.8 1.0; 1.0 0.6 0.6]);
title('井孔附近局部放大', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
ylabel('Z方向距离 (m)');

% 添加详细标注
hold on;
plot([med_x*dx, med_x*dx], [z_coords(zoom_range_z(1)), z_coords(zoom_range_z(end))], 'k--', 'LineWidth', 2);
plot([(med_x-l_cal)*dx, (med_x-l_cal)*dx], [z_coords(zoom_range_z(1)), z_coords(zoom_range_z(end))], 'b--');
plot([(med_x+l_cal)*dx, (med_x+l_cal)*dx], [z_coords(zoom_range_z(1)), z_coords(zoom_range_z(end))], 'b--');
plot([invasion_start_x*dx, invasion_start_x*dx], [z_coords(zoom_range_z(1)), z_coords(zoom_range_z(end))], 'r-', 'LineWidth', 2);
plot([invasion_end_x*dx, invasion_end_x*dx], [z_coords(zoom_range_z(1)), z_coords(zoom_range_z(end))], 'r-', 'LineWidth', 2);

% 径向剖面图
subplot(2, 2, 4);
center_z_idx = round(nz/2);
radial_profile = model(center_z_idx, :);
plot(x_coords, radial_profile, 'b-', 'LineWidth', 3);
title('径向剖面 (通过模型中心)', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
ylabel('介质类型');
ylim([0.5, 3.5]);
yticks([1, 2, 3]);
yticklabels({'地层', '井孔', '侵入带'});
grid on;

% 标注关键位置
hold on;
xline(med_x*dx, '--k', '井轴', 'LineWidth', 2);
xline((med_x-l_cal)*dx, '--b', '井孔左边界');
xline((med_x+l_cal)*dx, '--b', '井孔右边界');
xline(invasion_start_x*dx, '--r', '侵入带开始', 'LineWidth', 2);
xline(invasion_end_x*dx, '--r', '侵入带结束', 'LineWidth', 2);

sgtitle('侵入带几何建模位置分析', 'FontSize', 16, 'FontWeight', 'bold');

fprintf('可视化图像生成完成！\n');

%% 7. 总结
fprintf('\n7. 总结:\n');
fprintf('========\n');
fprintf('侵入带的几何位置定义非常明确:\n');
fprintf('• X方向: 紧邻井孔右侧，从第%d列到第%d列\n', invasion_start_x, invasion_end_x);
fprintf('• Z方向: 位于模型中心，从第%d行到第%d行\n', invasion_start_z, invasion_end_z);
fprintf('• 物理尺寸: %.3f×%.3f m (径向×轴向)\n', Formation_DIter*dx, Formation_HIter*dz);
fprintf('• 相对位置: 井孔右侧，模型垂直中心\n');
fprintf('\n这种定义方式简单直观，便于理解和修改！\n');
