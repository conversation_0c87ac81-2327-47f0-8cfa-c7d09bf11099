% DAS应变率计算方法对比测试
% 对比当前方法（两端点差分）vs 正确方法（平均应变率）

clear; clc;

% 参数设置
DELTAZ = 0.02;  % 网格间距（米）
gauge_length = 0.6;  % 标距长度（米）
gauge_points = round(gauge_length / DELTAZ);  % 标距包含的网格点数
fprintf('标距长度: %.2f米\n', gauge_length);
fprintf('网格间距: %.3f米\n', DELTAZ);
fprintf('标距包含网格点数: %d\n', gauge_points);

% 测试用例数量
num_tests = 100;

% 存储结果
method1_results = zeros(num_tests, 1);  % 当前方法（两端点差分）
method2_results = zeros(num_tests, 1);  % 正确方法（平均应变率）
differences = zeros(num_tests, 1);     % 两种方法的差异
relative_errors = zeros(num_tests, 1); % 相对误差

fprintf('\n开始进行 %d 次随机测试...\n', num_tests);

for test_idx = 1:num_tests
    % 生成随机速度分布
    % 使用不同类型的随机分布来测试
    switch mod(test_idx, 4)
        case 0  % 均匀随机分布
            velocities = rand(1, gauge_points + 1) * 10 - 5;  % -5到5的随机速度
        case 1  % 正弦波 + 噪声
            x = linspace(0, 2*pi, gauge_points + 1);
            velocities = 3*sin(x) + 0.5*randn(1, gauge_points + 1);
        case 2  % 指数衰减 + 噪声
            x = linspace(0, 3, gauge_points + 1);
            velocities = 5*exp(-x) + 0.3*randn(1, gauge_points + 1);
        case 3  % 多项式 + 噪声
            x = linspace(-1, 1, gauge_points + 1);
            velocities = 2*x.^3 - x.^2 + 3*x + 1 + 0.4*randn(1, gauge_points + 1);
    end
    
    % 方法1：当前程序的方法（两端点差分）
    v_start = velocities(1);
    v_end = velocities(end);
    actual_distance = gauge_points * DELTAZ;
    method1_strain_rate = (v_end - v_start) / actual_distance;
    method1_results(test_idx) = method1_strain_rate;
    
    % 方法2：正确的方法（标距内平均应变率）
    local_strain_rates = [];
    for i = 1:gauge_points
        local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
        local_strain_rates = [local_strain_rates, local_strain_rate];
    end
    method2_strain_rate = mean(local_strain_rates);
    method2_results(test_idx) = method2_strain_rate;
    
    % 计算差异
    differences(test_idx) = method1_strain_rate - method2_strain_rate;
    
    % 计算相对误差
    if abs(method2_strain_rate) > 1e-10
        relative_errors(test_idx) = abs(differences(test_idx)) / abs(method2_strain_rate) * 100;
    else
        relative_errors(test_idx) = 0;
    end
    
    % 显示前10个详细结果
    if test_idx <= 10
        fprintf('\n测试 %d:\n', test_idx);
        fprintf('  速度分布: [%.3f, %.3f, %.3f, ..., %.3f, %.3f]\n', ...
                velocities(1), velocities(2), velocities(3), velocities(end-1), velocities(end));
        fprintf('  方法1 (两端点): %.6f\n', method1_strain_rate);
        fprintf('  方法2 (平均值): %.6f\n', method2_strain_rate);
        fprintf('  差异: %.6f\n', differences(test_idx));
        fprintf('  相对误差: %.3f%%\n', relative_errors(test_idx));
    end
end

% 统计分析
fprintf('\n=== 统计结果分析 ===\n');
fprintf('测试次数: %d\n', num_tests);
fprintf('差异统计:\n');
fprintf('  平均差异: %.6f\n', mean(differences));
fprintf('  差异标准差: %.6f\n', std(differences));
fprintf('  最大差异: %.6f\n', max(abs(differences)));
fprintf('  最小差异: %.6f\n', min(abs(differences)));

fprintf('\n相对误差统计:\n');
fprintf('  平均相对误差: %.3f%%\n', mean(relative_errors));
fprintf('  相对误差标准差: %.3f%%\n', std(relative_errors));
fprintf('  最大相对误差: %.3f%%\n', max(relative_errors));

% 检查是否有显著差异
significant_diff_count = sum(abs(differences) > 1e-6);
fprintf('\n显著差异 (>1e-6) 的测试数量: %d / %d\n', significant_diff_count, num_tests);
fprintf('显著差异比例: %.1f%%\n', significant_diff_count/num_tests*100);

% 绘图对比
figure('Position', [100, 100, 1200, 800]);

% 子图1: 两种方法的结果对比
subplot(2, 3, 1);
plot(method1_results, method2_results, 'bo', 'MarkerSize', 4);
hold on;
plot([-max(abs([method1_results; method2_results])), max(abs([method1_results; method2_results]))], ...
     [-max(abs([method1_results; method2_results])), max(abs([method1_results; method2_results]))], 'r--');
xlabel('方法1 (两端点差分)');
ylabel('方法2 (平均应变率)');
title('两种方法结果对比');
grid on;
axis equal;

% 子图2: 差异分布
subplot(2, 3, 2);
histogram(differences, 20);
xlabel('差异 (方法1 - 方法2)');
ylabel('频次');
title('差异分布直方图');
grid on;

% 子图3: 相对误差分布
subplot(2, 3, 3);
histogram(relative_errors, 20);
xlabel('相对误差 (%)');
ylabel('频次');
title('相对误差分布');
grid on;

% 子图4: 差异随测试序号的变化
subplot(2, 3, 4);
plot(1:num_tests, differences, 'b-', 'LineWidth', 1);
xlabel('测试序号');
ylabel('差异');
title('差异随测试序号变化');
grid on;

% 子图5: 两种方法结果的时间序列
subplot(2, 3, 5);
plot(1:num_tests, method1_results, 'b-', 'LineWidth', 1, 'DisplayName', '方法1');
hold on;
plot(1:num_tests, method2_results, 'r--', 'LineWidth', 1, 'DisplayName', '方法2');
xlabel('测试序号');
ylabel('应变率');
title('两种方法结果对比');
legend;
grid on;

% 子图6: 相对误差随测试序号变化
subplot(2, 3, 6);
semilogy(1:num_tests, relative_errors + 1e-10, 'g-', 'LineWidth', 1);
xlabel('测试序号');
ylabel('相对误差 (%) [对数尺度]');
title('相对误差变化');
grid on;

sgtitle('DAS应变率计算方法对比分析', 'FontSize', 14, 'FontWeight', 'bold');

% 结论
fprintf('\n=== 结论 ===\n');
if mean(abs(differences)) < 1e-10
    fprintf('结论: 两种方法在数值上基本等价\n');
elseif mean(relative_errors) < 1
    fprintf('结论: 两种方法有小的差异，但在工程精度范围内\n');
else
    fprintf('结论: 两种方法存在显著差异！\n');
end

fprintf('建议: ');
if significant_diff_count > num_tests * 0.1
    fprintf('建议使用平均应变率方法以获得更准确的结果\n');
else
    fprintf('当前的两端点方法在大多数情况下是可接受的\n');
end
