% 更详细的DAS应变率计算方法对比测试
% 专门设计一些可能产生差异的特殊情况

clear; clc;

% 参数设置
DELTAZ = 0.02;  % 网格间距（米）
gauge_length = 0.6;  % 标距长度（米）
gauge_points = round(gauge_length / DELTAZ);  % 标距包含的网格点数

fprintf('=== 详细测试分析 ===\n');
fprintf('标距长度: %.2f米\n', gauge_length);
fprintf('网格间距: %.3f米\n', DELTAZ);
fprintf('标距包含网格点数: %d\n', gauge_points);

% 定义测试函数
function [method1_result, method2_result, diff, rel_error] = compare_methods(velocities, DELTAZ)
    gauge_points = length(velocities) - 1;
    
    % 方法1：两端点差分
    v_start = velocities(1);
    v_end = velocities(end);
    actual_distance = gauge_points * DELTAZ;
    method1_result = (v_end - v_start) / actual_distance;
    
    % 方法2：平均应变率
    local_strain_rates = [];
    for i = 1:gauge_points
        local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
        local_strain_rates = [local_strain_rates, local_strain_rate];
    end
    method2_result = mean(local_strain_rates);
    
    % 计算差异
    diff = method1_result - method2_result;
    if abs(method2_result) > 1e-15
        rel_error = abs(diff) / abs(method2_result) * 100;
    else
        rel_error = 0;
    end
end

% 测试案例
test_cases = {};

% 案例1：线性变化（理论上应该相等）
fprintf('\n=== 案例1：线性变化 ===\n');
velocities1 = linspace(0, 10, gauge_points + 1);
[m1, m2, diff, rel_err] = compare_methods(velocities1, DELTAZ);
fprintf('线性变化 (0到10): 方法1=%.6f, 方法2=%.6f, 差异=%.10f\n', m1, m2, diff);

% 案例2：常数（应该都为0）
fprintf('\n=== 案例2：常数 ===\n');
velocities2 = ones(1, gauge_points + 1) * 5;
[m1, m2, diff, rel_err] = compare_methods(velocities2, DELTAZ);
fprintf('常数5: 方法1=%.6f, 方法2=%.6f, 差异=%.10f\n', m1, m2, diff);

% 案例3：高频振荡
fprintf('\n=== 案例3：高频振荡 ===\n');
x = linspace(0, 10*pi, gauge_points + 1);
velocities3 = sin(x);
[m1, m2, diff, rel_err] = compare_methods(velocities3, DELTAZ);
fprintf('高频正弦: 方法1=%.6f, 方法2=%.6f, 差异=%.10f\n', m1, m2, diff);

% 案例4：阶跃函数
fprintf('\n=== 案例4：阶跃函数 ===\n');
velocities4 = [zeros(1, gauge_points/2), ones(1, gauge_points/2 + 1)];
[m1, m2, diff, rel_err] = compare_methods(velocities4, DELTAZ);
fprintf('阶跃函数: 方法1=%.6f, 方法2=%.6f, 差异=%.10f\n', m1, m2, diff);

% 案例5：随机噪声
fprintf('\n=== 案例5：随机噪声 ===\n');
rng(42); % 固定随机种子以便重现
velocities5 = randn(1, gauge_points + 1);
[m1, m2, diff, rel_err] = compare_methods(velocities5, DELTAZ);
fprintf('随机噪声: 方法1=%.6f, 方法2=%.6f, 差异=%.10f\n', m1, m2, diff);

% 案例6：二次函数
fprintf('\n=== 案例6：二次函数 ===\n');
x = linspace(-1, 1, gauge_points + 1);
velocities6 = x.^2;
[m1, m2, diff, rel_err] = compare_methods(velocities6, DELTAZ);
fprintf('二次函数: 方法1=%.6f, 方法2=%.6f, 差异=%.10f\n', m1, m2, diff);

% 案例7：指数函数
fprintf('\n=== 案例7：指数函数 ===\n');
x = linspace(0, 2, gauge_points + 1);
velocities7 = exp(x);
[m1, m2, diff, rel_err] = compare_methods(velocities7, DELTAZ);
fprintf('指数函数: 方法1=%.6f, 方法2=%.6f, 差异=%.10f\n', m1, m2, diff);

% 数学验证：证明两种方法的等价性
fprintf('\n=== 数学验证 ===\n');
fprintf('理论分析：\n');
fprintf('方法1: (v_end - v_start) / L_total\n');
fprintf('方法2: (1/N) * Σ[(v_{i+1} - v_i) / Δz]\n');
fprintf('      = (1/N) * (1/Δz) * Σ[v_{i+1} - v_i]\n');
fprintf('      = (1/N) * (1/Δz) * (v_end - v_start)  [望远镜求和]\n');
fprintf('      = (v_end - v_start) / (N * Δz)\n');
fprintf('      = (v_end - v_start) / L_total\n');
fprintf('因此两种方法在数学上完全等价！\n');

% 数值精度测试
fprintf('\n=== 数值精度测试 ===\n');
fprintf('测试极小数值的情况...\n');

% 极小数值
velocities_tiny = [1e-15, 2e-15, 1.5e-15, 3e-15, 2.5e-15];
velocities_tiny = [velocities_tiny, zeros(1, gauge_points + 1 - length(velocities_tiny))];
[m1, m2, diff, rel_err] = compare_methods(velocities_tiny, DELTAZ);
fprintf('极小数值: 方法1=%.15e, 方法2=%.15e, 差异=%.15e\n', m1, m2, diff);

% 极大数值
velocities_huge = [1e15, 2e15, 1.5e15, 3e15, 2.5e15];
velocities_huge = [velocities_huge, zeros(1, gauge_points + 1 - length(velocities_huge))];
[m1, m2, diff, rel_err] = compare_methods(velocities_huge, DELTAZ);
fprintf('极大数值: 方法1=%.6e, 方法2=%.6e, 差异=%.6e\n', m1, m2, diff);

% 可视化一个具体例子
fprintf('\n=== 可视化分析 ===\n');
figure('Position', [100, 100, 1200, 600]);

% 选择一个有趣的速度分布进行可视化
x_pos = linspace(0, gauge_length, gauge_points + 1);
velocities_vis = sin(2*pi*x_pos/gauge_length) + 0.5*cos(6*pi*x_pos/gauge_length);

% 计算局部应变率
local_strain_rates = [];
for i = 1:gauge_points
    local_strain_rate = (velocities_vis(i+1) - velocities_vis(i)) / DELTAZ;
    local_strain_rates = [local_strain_rates, local_strain_rate];
end

% 计算两种方法的结果
[m1_vis, m2_vis, diff_vis, rel_err_vis] = compare_methods(velocities_vis, DELTAZ);

% 绘图
subplot(1, 2, 1);
plot(x_pos, velocities_vis, 'b-o', 'LineWidth', 2, 'MarkerSize', 4);
xlabel('位置 (m)');
ylabel('速度');
title('速度分布');
grid on;

subplot(1, 2, 2);
x_strain = x_pos(1:end-1) + DELTAZ/2;  % 应变率的位置在网格中点
plot(x_strain, local_strain_rates, 'r-s', 'LineWidth', 1.5, 'MarkerSize', 4);
hold on;
plot([x_pos(1), x_pos(end)], [m1_vis, m1_vis], 'g--', 'LineWidth', 2, 'DisplayName', '方法1 (两端点)');
plot([x_pos(1), x_pos(end)], [m2_vis, m2_vis], 'b:', 'LineWidth', 2, 'DisplayName', '方法2 (平均值)');
xlabel('位置 (m)');
ylabel('应变率');
title('应变率分布');
legend('局部应变率', '方法1', '方法2');
grid on;

sgtitle(sprintf('DAS应变率计算对比 (差异: %.2e)', diff_vis), 'FontSize', 14);

fprintf('可视化案例: 方法1=%.10f, 方法2=%.10f, 差异=%.15f\n', m1_vis, m2_vis, diff_vis);

% 最终结论
fprintf('\n=== 最终结论 ===\n');
fprintf('1. 数学理论证明两种方法完全等价\n');
fprintf('2. 数值测试验证了理论结果\n');
fprintf('3. 在所有测试案例中，差异都在机器精度范围内\n');
fprintf('4. 当前程序使用的两端点方法是正确的\n');
fprintf('5. 从计算效率角度，两端点方法更优\n');
