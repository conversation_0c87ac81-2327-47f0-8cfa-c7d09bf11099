% 分析DAS标距内"每一小段"的定义和取值
% 基于main.m程序的参数设置

clear; clc;

fprintf('=== DAS标距内"每一小段"定义分析 ===\n');

% 从main.m程序中提取的关键参数
vp1 = 1500;        % 井孔内纵波速度 [m/s]
f0 = 10*10^3;      % 主频率 [Hz]
gauge_length = 0.6; % 标距长度 [m]

% 计算网格参数
la1 = vp1/f0;      % 波长计算 [m]
dz = la1/10;       % Z方向空间步长 [m] （波长/10）

fprintf('基础参数:\n');
fprintf('  主频率 f0 = %d Hz\n', f0);
fprintf('  井孔波速 vp1 = %d m/s\n', vp1);
fprintf('  波长 λ = vp1/f0 = %.4f m\n', la1);
fprintf('  网格步长 dz = λ/10 = %.4f m\n', dz);
fprintf('  标距长度 gauge_length = %.1f m\n', gauge_length);

% 计算标距相关参数
gauge_half_length_grid = round(gauge_length/(2*dz));  % 标距半长度（网格点数）
total_grid_points = 2 * gauge_half_length_grid + 1;   % 标距总网格点数
actual_gauge_length = total_grid_points * dz;         % 实际标距长度

fprintf('\n标距网格化:\n');
fprintf('  标距半长度网格点数 = round(%.1f/(2×%.4f)) = %d 点\n', ...
    gauge_length, dz, gauge_half_length_grid);
fprintf('  标距总网格点数 = 2×%d+1 = %d 点\n', gauge_half_length_grid, total_grid_points);
fprintf('  实际标距长度 = %d×%.4f = %.4f m\n', total_grid_points, dz, actual_gauge_length);

% 分析"每一小段"的定义
num_segments = total_grid_points - 1;  % 小段数量
segment_length = dz;                   % 每小段长度

fprintf('\n"每一小段"定义:\n');
fprintf('  小段数量 = 总网格点数-1 = %d-1 = %d 段\n', total_grid_points, num_segments);
fprintf('  每小段长度 = dz = %.4f m\n', segment_length);
fprintf('  每小段长度 = %.2f mm\n', segment_length*1000);

% 详细说明计算过程
fprintf('\n计算过程详解:\n');
fprintf('对于标距内的每一小段 k:\n');
fprintf('  位置范围: 从 gauge_start 到 gauge_end-1\n');
fprintf('  小段 k 的应变率 = (Vz(k+1) - Vz(k)) / dz\n');
fprintf('  其中:\n');
fprintf('    - Vz(k): 第k个网格点的Z方向速度\n');
fprintf('    - Vz(k+1): 第k+1个网格点的Z方向速度\n');
fprintf('    - dz = %.4f m: 网格间距\n', dz);

% 可视化示例
fprintf('\n可视化示例:\n');
fprintf('假设一个标距包含 %d 个网格点:\n', total_grid_points);

% 创建示例网格点
grid_positions = (0:total_grid_points-1) * dz;
fprintf('网格点位置: ');
for i = 1:min(5, total_grid_points)
    fprintf('%.3fm ', grid_positions(i));
end
if total_grid_points > 5
    fprintf('... ');
    for i = max(1, total_grid_points-2):total_grid_points
        fprintf('%.3fm ', grid_positions(i));
    end
end
fprintf('\n');

fprintf('小段定义:\n');
for i = 1:min(3, num_segments)
    fprintf('  小段%d: 从网格点%d(%.3fm) 到 网格点%d(%.3fm), 长度=%.4fm\n', ...
        i, i-1, grid_positions(i), i, grid_positions(i+1), dz);
end
if num_segments > 3
    fprintf('  ...\n');
    fprintf('  小段%d: 从网格点%d(%.3fm) 到 网格点%d(%.3fm), 长度=%.4fm\n', ...
        num_segments, num_segments-1, grid_positions(num_segments), ...
        num_segments, grid_positions(num_segments+1), dz);
end

% 应变率计算示例
fprintf('\n应变率计算示例:\n');
fprintf('每小段的应变率计算:\n');
for i = 1:min(3, num_segments)
    fprintf('  小段%d应变率 = (Vz(%d) - Vz(%d)) / %.4f\n', ...
        i, i, i-1, dz);
end
if num_segments > 3
    fprintf('  ...\n');
end

fprintf('\n最终平均应变率:\n');
fprintf('  平均应变率 = (所有小段应变率之和) / 小段总数\n');
fprintf('  平均应变率 = Σ[小段应变率] / %d\n', num_segments);

% 数值精度分析
fprintf('\n数值精度分析:\n');
fprintf('  网格分辨率: %.2f 点/波长\n', 1/dz * la1);
fprintf('  标距长度包含: %.1f 个波长\n', gauge_length/la1);
fprintf('  每小段包含: %.3f 个波长\n', dz/la1);

% 与理论公式的关系
fprintf('\n与理论公式的关系:\n');
fprintf('  理论: 应变率 = ∂Vz/∂z\n');
fprintf('  数值近似: ∂Vz/∂z ≈ (Vz(k+1) - Vz(k)) / dz\n');
fprintf('  平均化: 应变率 = (1/N) × Σ[(Vz(k+1) - Vz(k)) / dz]\n');
fprintf('  其中 N = %d (小段总数)\n', num_segments);

% 物理意义
fprintf('\n物理意义:\n');
fprintf('  每小段代表光纤的一个微小段落\n');
fprintf('  小段长度 %.2f mm 远小于声波波长 %.1f mm\n', dz*1000, la1*1000);
fprintf('  这保证了应变率计算的空间分辨率\n');
fprintf('  通过平均化，减少了数值噪声的影响\n');

% 与传统两端点方法对比
fprintf('\n与传统两端点方法对比:\n');
fprintf('  传统方法: 应变率 = (Vz_end - Vz_start) / 标距长度\n');
fprintf('  当前方法: 应变率 = 平均[(Vz(k+1) - Vz(k)) / dz]\n');
fprintf('  优势: 当前方法考虑了标距内的所有空间变化\n');
fprintf('  数学等价性: 在理想情况下两种方法结果相同\n');

fprintf('\n=== 分析完成 ===\n');
