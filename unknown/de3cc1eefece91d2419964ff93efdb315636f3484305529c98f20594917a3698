% DAS在多地层情况下的应变率计算测试
% 验证当标距跨越不同地层时，两种方法是否还等价

clear; clc;

fprintf('=== DAS多地层应变率计算测试 ===\n');

% 参数设置
DELTAZ = 0.02;  % 网格间距（米）
gauge_length = 0.6;  % 标距长度（米）
gauge_points = round(gauge_length / DELTAZ);  % 标距包含的网格点数

fprintf('标距长度: %.2f米\n', gauge_length);
fprintf('网格间距: %.3f米\n', DELTAZ);
fprintf('标距包含网格点数: %d\n', gauge_points);

% 定义多地层模型
% 地层1: 0-0.2m (软岩)
% 地层2: 0.2-0.4m (硬岩) 
% 地层3: 0.4-0.6m (软岩)

layer_boundaries = [0, 0.2, 0.4, 0.6];  % 地层边界
layer_properties = [
    2000, 2000;  % 地层1: Vp=2000m/s, 密度=2000kg/m³
    4000, 2600;  % 地层2: Vp=4000m/s, 密度=2600kg/m³  
    2000, 2000   % 地层3: Vp=2000m/s, 密度=2000kg/m³
];

fprintf('\n地层模型:\n');
for i = 1:length(layer_boundaries)-1
    fprintf('地层%d: %.1f-%.1fm, Vp=%dm/s, 密度=%dkg/m³\n', ...
        i, layer_boundaries(i), layer_boundaries(i+1), ...
        layer_properties(i,1), layer_properties(i,2));
end

% 创建位置数组
positions = (0:gauge_points) * DELTAZ;

% 测试不同的波传播情况
test_cases = {
    '均匀波场', 
    '平面波传播',
    '反射波干涉',
    '频散效应',
    '界面反射'
};

fprintf('\n开始测试不同波场情况...\n');

for test_idx = 1:length(test_cases)
    fprintf('\n=== %s ===\n', test_cases{test_idx});
    
    % 根据不同测试案例生成速度场
    switch test_idx
        case 1  % 均匀波场
            velocities = ones(1, gauge_points + 1) * 0.01;  % 均匀速度
            
        case 2  % 平面波传播 - 考虑不同地层的波速
            % 简单平面波，但在不同地层中有不同的波长
            velocities = zeros(1, gauge_points + 1);
            for i = 1:length(positions)
                pos = positions(i);
                % 确定当前位置属于哪个地层
                layer_idx = find(pos >= layer_boundaries(1:end-1) & pos < layer_boundaries(2:end));
                if isempty(layer_idx)
                    layer_idx = length(layer_boundaries) - 1;
                end
                
                % 根据地层波速计算相位
                vp = layer_properties(layer_idx, 1);
                freq = 100;  % 100Hz
                wavelength = vp / freq;
                velocities(i) = 0.01 * sin(2*pi*pos/wavelength);
            end
            
        case 3  % 反射波干涉
            velocities = zeros(1, gauge_points + 1);
            for i = 1:length(positions)
                pos = positions(i);
                % 入射波 + 反射波
                incident = 0.01 * sin(2*pi*pos/0.1);
                reflected = 0.005 * sin(2*pi*(gauge_length-pos)/0.1 + pi);
                velocities(i) = incident + reflected;
            end
            
        case 4  % 频散效应
            velocities = zeros(1, gauge_points + 1);
            for i = 1:length(positions)
                pos = positions(i);
                % 高频和低频成分的叠加，在不同地层中传播速度不同
                high_freq = 0.005 * sin(2*pi*pos/0.05);
                low_freq = 0.008 * sin(2*pi*pos/0.2);
                velocities(i) = high_freq + low_freq;
            end
            
        case 5  % 界面反射
            velocities = zeros(1, gauge_points + 1);
            for i = 1:length(positions)
                pos = positions(i);
                % 在地层界面处有强反射
                base_wave = 0.01 * sin(2*pi*pos/0.15);
                
                % 在界面处添加反射
                reflection_strength = 0;
                for boundary = layer_boundaries(2:end-1)
                    if abs(pos - boundary) < 0.05
                        reflection_strength = reflection_strength + 0.003 * exp(-((pos-boundary)/0.02)^2);
                    end
                end
                
                velocities(i) = base_wave + reflection_strength;
            end
    end
    
    % 方法1：两端点差分
    v_start = velocities(1);
    v_end = velocities(end);
    method1_strain_rate = (v_end - v_start) / gauge_length;
    
    % 方法2：平均应变率
    local_strain_rates = [];
    for i = 1:gauge_points
        local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
        local_strain_rates = [local_strain_rates, local_strain_rate];
    end
    method2_strain_rate = mean(local_strain_rates);
    
    % 方法3：加权平均应变率（考虑地层属性）
    weighted_strain_rates = [];
    weights = [];
    for i = 1:gauge_points
        pos_center = (i-0.5) * DELTAZ;
        % 确定当前段属于哪个地层
        layer_idx = find(pos_center >= layer_boundaries(1:end-1) & pos_center < layer_boundaries(2:end));
        if isempty(layer_idx)
            layer_idx = length(layer_boundaries) - 1;
        end
        
        local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
        density = layer_properties(layer_idx, 2);
        
        weighted_strain_rates = [weighted_strain_rates, local_strain_rate * density];
        weights = [weights, density];
    end
    method3_strain_rate = sum(weighted_strain_rates) / sum(weights);
    
    % 计算差异
    diff_1_2 = method1_strain_rate - method2_strain_rate;
    diff_1_3 = method1_strain_rate - method3_strain_rate;
    diff_2_3 = method2_strain_rate - method3_strain_rate;
    
    % 显示结果
    fprintf('方法1 (两端点): %.8f s⁻¹\n', method1_strain_rate);
    fprintf('方法2 (平均值): %.8f s⁻¹\n', method2_strain_rate);
    fprintf('方法3 (加权平均): %.8f s⁻¹\n', method3_strain_rate);
    fprintf('差异 (1-2): %.2e\n', diff_1_2);
    fprintf('差异 (1-3): %.2e\n', diff_1_3);
    fprintf('差异 (2-3): %.2e\n', diff_2_3);
    
    % 分析每个地层内的应变率
    fprintf('各地层内的平均应变率:\n');
    for layer = 1:length(layer_boundaries)-1
        layer_start_idx = round(layer_boundaries(layer) / DELTAZ) + 1;
        layer_end_idx = round(layer_boundaries(layer+1) / DELTAZ);
        
        if layer_end_idx > length(velocities)
            layer_end_idx = length(velocities);
        end
        
        if layer_start_idx < layer_end_idx
            layer_velocities = velocities(layer_start_idx:layer_end_idx);
            layer_length = (layer_end_idx - layer_start_idx) * DELTAZ;
            layer_strain_rate = (layer_velocities(end) - layer_velocities(1)) / layer_length;
            fprintf('  地层%d: %.8f s⁻¹\n', layer, layer_strain_rate);
        end
    end
end

% 创建可视化
fprintf('\n生成可视化图形...\n');
figure('Position', [100, 100, 1400, 800]);

% 最后一个测试案例的详细可视化
test_idx = 5;  % 界面反射案例

% 重新计算最后一个案例的数据用于绘图
velocities = zeros(1, gauge_points + 1);
for i = 1:length(positions)
    pos = positions(i);
    base_wave = 0.01 * sin(2*pi*pos/0.15);
    reflection_strength = 0;
    for boundary = layer_boundaries(2:end-1)
        if abs(pos - boundary) < 0.05
            reflection_strength = reflection_strength + 0.003 * exp(-((pos-boundary)/0.02)^2);
        end
    end
    velocities(i) = base_wave + reflection_strength;
end

% 计算局部应变率
local_strain_rates = [];
strain_positions = [];
for i = 1:gauge_points
    local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
    local_strain_rates = [local_strain_rates, local_strain_rate];
    strain_positions = [strain_positions, (i-0.5) * DELTAZ];
end

% 子图1: 速度分布
subplot(2, 2, 1);
plot(positions, velocities, 'b-o', 'LineWidth', 2, 'MarkerSize', 4);
hold on;
% 标记地层界面
for boundary = layer_boundaries(2:end-1)
    xline(boundary, 'r--', 'LineWidth', 2);
end
xlabel('位置 (m)');
ylabel('速度 (m/s)');
title('速度分布（界面反射案例）');
grid on;
legend('速度', '地层界面');

% 子图2: 局部应变率分布
subplot(2, 2, 2);
plot(strain_positions, local_strain_rates, 'g-s', 'LineWidth', 1.5, 'MarkerSize', 4);
hold on;
% 标记地层界面
for boundary = layer_boundaries(2:end-1)
    xline(boundary, 'r--', 'LineWidth', 2);
end
% 标记平均值
avg_strain = mean(local_strain_rates);
yline(avg_strain, 'b:', 'LineWidth', 2, 'DisplayName', '平均应变率');
xlabel('位置 (m)');
ylabel('应变率 (s⁻¹)');
title('局部应变率分布');
grid on;
legend('局部应变率', '地层界面', '平均值');

% 子图3: 地层属性
subplot(2, 2, 3);
layer_centers = [];
vp_values = [];
density_values = [];
for i = 1:length(layer_boundaries)-1
    center = (layer_boundaries(i) + layer_boundaries(i+1)) / 2;
    layer_centers = [layer_centers, center];
    vp_values = [vp_values, layer_properties(i, 1)];
    density_values = [density_values, layer_properties(i, 2)];
end

yyaxis left;
bar(layer_centers, vp_values, 0.15, 'FaceColor', 'blue', 'FaceAlpha', 0.7);
ylabel('P波速度 (m/s)');

yyaxis right;
bar(layer_centers + 0.05, density_values, 0.15, 'FaceColor', 'red', 'FaceAlpha', 0.7);
ylabel('密度 (kg/m³)');

xlabel('地层中心位置 (m)');
title('地层属性分布');
grid on;

% 子图4: 方法对比总结
subplot(2, 2, 4);
methods = {'两端点', '平均值', '加权平均'};
% 使用界面反射案例的结果
strain_values = [method1_strain_rate, method2_strain_rate, method3_strain_rate];
bar(strain_values, 'FaceColor', [0.7, 0.7, 0.9]);
set(gca, 'XTickLabel', methods);
ylabel('应变率 (s⁻¹)');
title('不同计算方法对比');
grid on;

sgtitle('DAS多地层应变率计算分析', 'FontSize', 14, 'FontWeight', 'bold');

% 总结
fprintf('\n=== 总结 ===\n');
fprintf('1. 在多地层情况下，数学等价性依然成立\n');
fprintf('2. 但物理解释变得复杂：\n');
fprintf('   - 不同地层有不同的波传播特性\n');
fprintf('   - 界面反射会产生复杂的波场\n');
fprintf('   - 简单的两端点方法可能丢失局部细节\n');
fprintf('3. 建议：\n');
fprintf('   - 对于均匀介质，两端点方法完全有效\n');
fprintf('   - 对于复杂地层，需要考虑加权平均\n');
fprintf('   - 标距长度应小于地层厚度以获得更好的分辨率\n');
