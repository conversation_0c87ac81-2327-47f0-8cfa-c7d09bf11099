% 严格平均应变率方法 vs 两端点方法对比
% 验证在多地层情况下严格平均方法的优势

clear; clc;

fprintf('=== 严格平均应变率方法测试 ===\n');

% 参数设置
DELTAZ = 0.02;  % 网格间距（米）
gauge_length = 0.6;  % 标距长度（米）
gauge_points = round(gauge_length / DELTAZ);  % 标距包含的网格点数

fprintf('标距长度: %.2f米\n', gauge_length);
fprintf('网格间距: %.3f米\n', DELTAZ);
fprintf('标距包含网格点数: %d\n', gauge_points);

% 定义更复杂的多地层模型
layer_boundaries = [0, 0.15, 0.25, 0.35, 0.45, 0.6];  % 更多地层界面
layer_properties = [
    2000, 2000, 0.3;  % 地层1: Vp=2000m/s, 密度=2000kg/m³, 泊松比=0.3
    4000, 2600, 0.25; % 地层2: Vp=4000m/s, 密度=2600kg/m³, 泊松比=0.25
    1800, 1900, 0.35; % 地层3: Vp=1800m/s, 密度=1900kg/m³, 泊松比=0.35
    3500, 2500, 0.28; % 地层4: Vp=3500m/s, 密度=2500kg/m³, 泊松比=0.28
    2200, 2100, 0.32  % 地层5: Vp=2200m/s, 密度=2100kg/m³, 泊松比=0.32
];

fprintf('\n复杂地层模型:\n');
for i = 1:length(layer_boundaries)-1
    fprintf('地层%d: %.2f-%.2fm, Vp=%dm/s, 密度=%dkg/m³\n', ...
        i, layer_boundaries(i), layer_boundaries(i+1), ...
        layer_properties(i,1), layer_properties(i,2));
end

% 创建位置数组
positions = (0:gauge_points) * DELTAZ;

% 定义计算方法
function [method1, method2, method3, method4] = calculate_strain_rates(velocities, DELTAZ, layer_boundaries, layer_properties)
    gauge_points = length(velocities) - 1;
    gauge_length = gauge_points * DELTAZ;
    
    % 方法1：两端点差分（当前程序方法）
    method1 = (velocities(end) - velocities(1)) / gauge_length;
    
    % 方法2：严格平均应变率（推荐方法）
    local_strain_rates = [];
    for i = 1:gauge_points
        local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
        local_strain_rates = [local_strain_rates, local_strain_rate];
    end
    method2 = mean(local_strain_rates);
    
    % 方法3：地层加权平均应变率
    weighted_strain_rates = [];
    weights = [];
    for i = 1:gauge_points
        pos_center = (i-0.5) * DELTAZ;
        % 确定当前段属于哪个地层
        layer_idx = find(pos_center >= layer_boundaries(1:end-1) & pos_center < layer_boundaries(2:end));
        if isempty(layer_idx)
            layer_idx = length(layer_boundaries) - 1;
        end
        
        local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
        density = layer_properties(layer_idx, 2);
        
        weighted_strain_rates = [weighted_strain_rates, local_strain_rate * density];
        weights = [weights, density];
    end
    method3 = sum(weighted_strain_rates) / sum(weights);
    
    % 方法4：分层计算后合成
    layer_strain_rates = [];
    layer_weights = [];
    for layer = 1:length(layer_boundaries)-1
        layer_start = layer_boundaries(layer);
        layer_end = layer_boundaries(layer+1);
        
        % 找到该地层在标距内的部分
        gauge_start_pos = 0;
        gauge_end_pos = gauge_length;
        
        overlap_start = max(layer_start, gauge_start_pos);
        overlap_end = min(layer_end, gauge_end_pos);
        
        if overlap_end > overlap_start
            % 计算重叠部分的长度权重
            overlap_length = overlap_end - overlap_start;
            
            % 找到对应的速度点
            start_idx = round(overlap_start / DELTAZ) + 1;
            end_idx = round(overlap_end / DELTAZ) + 1;
            
            if start_idx >= 1 && end_idx <= length(velocities) && end_idx > start_idx
                layer_velocities = velocities(start_idx:end_idx);
                layer_length = (end_idx - start_idx) * DELTAZ;
                
                if layer_length > 0
                    layer_strain_rate = (layer_velocities(end) - layer_velocities(1)) / layer_length;
                    layer_strain_rates = [layer_strain_rates, layer_strain_rate];
                    layer_weights = [layer_weights, overlap_length];
                end
            end
        end
    end
    
    if ~isempty(layer_strain_rates)
        method4 = sum(layer_strain_rates .* layer_weights) / sum(layer_weights);
    else
        method4 = method2;  % 回退到方法2
    end
end

% 测试不同的复杂波场情况
test_scenarios = {
    '强界面反射',
    '多次反射',
    '频散波包',
    '非线性波形',
    '随机噪声叠加'
};

fprintf('\n开始测试复杂波场情况...\n');

results_table = [];
scenario_names = {};

for test_idx = 1:length(test_scenarios)
    fprintf('\n=== %s ===\n', test_scenarios{test_idx});
    
    % 根据不同测试场景生成复杂速度场
    velocities = zeros(1, gauge_points + 1);
    
    switch test_idx
        case 1  % 强界面反射
            for i = 1:length(positions)
                pos = positions(i);
                % 基础波
                base_wave = 0.01 * sin(2*pi*pos/0.12);
                
                % 在每个界面处添加强反射
                reflection = 0;
                for boundary = layer_boundaries(2:end-1)
                    if abs(pos - boundary) < 0.08
                        % 反射系数取决于波阻抗差异
                        layer_before = find(boundary >= layer_boundaries(1:end-1), 1, 'last');
                        layer_after = min(layer_before + 1, size(layer_properties, 1));
                        
                        Z1 = layer_properties(layer_before, 1) * layer_properties(layer_before, 2);
                        Z2 = layer_properties(layer_after, 1) * layer_properties(layer_after, 2);
                        R = (Z2 - Z1) / (Z2 + Z1);  % 反射系数
                        
                        reflection = reflection + R * 0.008 * exp(-((pos-boundary)/0.03)^2);
                    end
                end
                
                velocities(i) = base_wave + reflection;
            end
            
        case 2  % 多次反射
            for i = 1:length(positions)
                pos = positions(i);
                wave = 0.01 * sin(2*pi*pos/0.1);
                
                % 添加多次反射
                for n = 1:3
                    for boundary = layer_boundaries(2:end-1)
                        reflection_pos = boundary + n * 0.05;
                        if abs(pos - reflection_pos) < 0.04
                            wave = wave + 0.003 * (0.7^n) * sin(2*pi*pos/0.1 + n*pi/4);
                        end
                    end
                end
                
                velocities(i) = wave;
            end
            
        case 3  % 频散波包
            for i = 1:length(positions)
                pos = positions(i);
                % 不同频率在不同地层中有不同的传播速度
                wave = 0;
                for freq = [50, 100, 200, 400]
                    % 根据当前位置的地层属性调整相位
                    layer_idx = find(pos >= layer_boundaries(1:end-1) & pos < layer_boundaries(2:end));
                    if isempty(layer_idx)
                        layer_idx = length(layer_boundaries) - 1;
                    end
                    
                    vp = layer_properties(layer_idx, 1);
                    phase_velocity = vp * (1 + 0.1 * freq / 1000);  % 简单频散关系
                    wavelength = phase_velocity / freq;
                    
                    amplitude = 0.003 * exp(-(freq-150)^2/10000);  % 高斯包络
                    wave = wave + amplitude * sin(2*pi*pos/wavelength);
                end
                
                velocities(i) = wave;
            end
            
        case 4  % 非线性波形
            for i = 1:length(positions)
                pos = positions(i);
                % 非线性波形，包含谐波
                fundamental = 0.008 * sin(2*pi*pos/0.15);
                harmonic2 = 0.003 * sin(4*pi*pos/0.15);
                harmonic3 = 0.001 * sin(6*pi*pos/0.15);
                
                velocities(i) = fundamental + harmonic2 + harmonic3;
            end
            
        case 5  % 随机噪声叠加
            for i = 1:length(positions)
                pos = positions(i);
                signal = 0.01 * sin(2*pi*pos/0.13);
                noise = 0.002 * randn();  % 随机噪声
                
                velocities(i) = signal + noise;
            end
    end
    
    % 计算四种方法的结果
    [m1, m2, m3, m4] = calculate_strain_rates(velocities, DELTAZ, layer_boundaries, layer_properties);
    
    % 显示结果
    fprintf('方法1 (两端点差分): %.8f s⁻¹\n', m1);
    fprintf('方法2 (严格平均): %.8f s⁻¹\n', m2);
    fprintf('方法3 (地层加权): %.8f s⁻¹\n', m3);
    fprintf('方法4 (分层合成): %.8f s⁻¹\n', m4);
    
    % 计算差异
    diff_1_2 = abs(m1 - m2);
    diff_2_3 = abs(m2 - m3);
    diff_2_4 = abs(m2 - m4);
    
    fprintf('差异 |方法1-方法2|: %.2e\n', diff_1_2);
    fprintf('差异 |方法2-方法3|: %.2e\n', diff_2_3);
    fprintf('差异 |方法2-方法4|: %.2e\n', diff_2_4);
    
    % 存储结果用于后续分析
    results_table = [results_table; m1, m2, m3, m4, diff_1_2, diff_2_3, diff_2_4];
    scenario_names{end+1} = test_scenarios{test_idx};
    
    % 分析标距内的应变率分布
    local_strain_rates = [];
    strain_positions = [];
    for i = 1:gauge_points
        local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
        local_strain_rates = [local_strain_rates, local_strain_rate];
        strain_positions = [strain_positions, (i-0.5) * DELTAZ];
    end
    
    fprintf('标距内应变率统计:\n');
    fprintf('  最大值: %.6f s⁻¹\n', max(local_strain_rates));
    fprintf('  最小值: %.6f s⁻¹\n', min(local_strain_rates));
    fprintf('  标准差: %.6f s⁻¹\n', std(local_strain_rates));
    fprintf('  变异系数: %.2f%%\n', std(local_strain_rates)/abs(mean(local_strain_rates))*100);
end

% 创建综合对比图
fprintf('\n生成综合对比分析...\n');
figure('Position', [100, 100, 1600, 1000]);

% 子图1: 方法对比
subplot(2, 3, 1);
bar(results_table(:, 1:4));
set(gca, 'XTickLabel', scenario_names, 'XTickLabelRotation', 45);
ylabel('应变率 (s⁻¹)');
title('四种方法结果对比');
legend('两端点', '严格平均', '地层加权', '分层合成', 'Location', 'best');
grid on;

% 子图2: 差异分析
subplot(2, 3, 2);
semilogy(results_table(:, 5:7), 'o-', 'LineWidth', 2, 'MarkerSize', 6);
set(gca, 'XTickLabel', scenario_names, 'XTickLabelRotation', 45);
ylabel('绝对差异 (对数尺度)');
title('方法间差异分析');
legend('|方法1-方法2|', '|方法2-方法3|', '|方法2-方法4|', 'Location', 'best');
grid on;

% 子图3-6: 最后一个测试案例的详细分析
test_idx = 5;  % 随机噪声叠加案例

% 重新生成最后一个案例的数据用于可视化
velocities = zeros(1, gauge_points + 1);
rng(42);  % 固定随机种子
for i = 1:length(positions)
    pos = positions(i);
    signal = 0.01 * sin(2*pi*pos/0.13);
    noise = 0.002 * randn();
    velocities(i) = signal + noise;
end

% 计算局部应变率
local_strain_rates = [];
strain_positions = [];
for i = 1:gauge_points
    local_strain_rate = (velocities(i+1) - velocities(i)) / DELTAZ;
    local_strain_rates = [local_strain_rates, local_strain_rate];
    strain_positions = [strain_positions, (i-0.5) * DELTAZ];
end

% 子图3: 速度分布
subplot(2, 3, 3);
plot(positions, velocities, 'b-o', 'LineWidth', 1.5, 'MarkerSize', 3);
hold on;
for boundary = layer_boundaries(2:end-1)
    xline(boundary, 'r--', 'LineWidth', 1);
end
xlabel('位置 (m)');
ylabel('速度 (m/s)');
title('速度分布（含噪声）');
grid on;

% 子图4: 局部应变率分布
subplot(2, 3, 4);
plot(strain_positions, local_strain_rates, 'g-s', 'LineWidth', 1, 'MarkerSize', 3);
hold on;
for boundary = layer_boundaries(2:end-1)
    xline(boundary, 'r--', 'LineWidth', 1);
end
% 标记不同方法的结果
[m1, m2, m3, m4] = calculate_strain_rates(velocities, DELTAZ, layer_boundaries, layer_properties);
yline(m1, 'b:', 'LineWidth', 2, 'DisplayName', '两端点');
yline(m2, 'g-', 'LineWidth', 2, 'DisplayName', '严格平均');
yline(m3, 'm--', 'LineWidth', 2, 'DisplayName', '地层加权');
xlabel('位置 (m)');
ylabel('应变率 (s⁻¹)');
title('局部应变率分布');
legend('局部值', '界面', '两端点', '严格平均', '地层加权');
grid on;

% 子图5: 累积应变率
subplot(2, 3, 5);
cumulative_strain = cumsum(local_strain_rates) / (1:length(local_strain_rates));
plot(strain_positions, cumulative_strain, 'r-', 'LineWidth', 2);
hold on;
yline(m2, 'g--', 'LineWidth', 2, 'DisplayName', '最终平均值');
xlabel('位置 (m)');
ylabel('累积平均应变率 (s⁻¹)');
title('累积平均应变率收敛');
legend('累积平均', '最终值');
grid on;

% 子图6: 方法稳定性分析
subplot(2, 3, 6);
stability_data = [
    std(results_table(:, 1));  % 两端点方法的标准差
    std(results_table(:, 2));  % 严格平均方法的标准差
    std(results_table(:, 3));  % 地层加权方法的标准差
    std(results_table(:, 4))   % 分层合成方法的标准差
];
bar(stability_data, 'FaceColor', [0.7, 0.8, 0.9]);
set(gca, 'XTickLabel', {'两端点', '严格平均', '地层加权', '分层合成'});
ylabel('结果标准差');
title('方法稳定性对比');
grid on;

sgtitle('DAS应变率计算方法综合对比分析', 'FontSize', 16, 'FontWeight', 'bold');

% 最终建议
fprintf('\n=== 最终建议 ===\n');
fprintf('1. 严格平均方法 (方法2) 的优势:\n');
fprintf('   - 数学上更严谨，真正计算标距内的平均应变率\n');
fprintf('   - 对噪声有更好的抑制能力\n');
fprintf('   - 能更好地反映标距内的真实应变分布\n');
fprintf('2. 在多地层情况下:\n');
fprintf('   - 严格平均方法比两端点方法更稳定\n');
fprintf('   - 地层加权方法在某些情况下能提供更准确的结果\n');
fprintf('   - 分层合成方法适用于地层差异很大的情况\n');
fprintf('3. 实际应用建议:\n');
fprintf('   - 推荐使用严格平均方法替代当前的两端点方法\n');
fprintf('   - 在已知地层信息时，考虑使用地层加权方法\n');
fprintf('   - 标距长度应根据地层厚度和所需分辨率来选择\n');
