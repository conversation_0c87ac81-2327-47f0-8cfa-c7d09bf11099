% 三维声波测井标准显示程序
% 基于huitu_best.m，保留所有功能，适配三维检波器和DAS数据
% 支持检波器数据和DAS数据的独立或对比显示

% ==================== 快速设置说明 ====================
% 如果觉得杂波太多，请调整以下两个主要参数：
%
% A. 振幅模式 (amplitude_mode，在第49行)：
%    1 = 传统归一化（杂波最少，但振幅信息丢失）
%    2 = 轻微保留振幅差异
%    3 = 中等保留振幅差异
%    4 = 强烈保留振幅差异（杂波较多，但保留真实振幅关系）
%
% B. 滤波强度 (filter_strength，在第44行)：
%    0 = 不滤波（保留所有细节，杂波最多）
%    1 = 轻微滤波（去除部分高频噪声）
%    2 = 正常滤波（推荐设置）
%    3 = 稍强滤波（进一步减少杂波）
%    4 = 强滤波（杂波最少，但可能丢失部分信号细节）
%
% 推荐组合：
% - 清晰显示：amplitude_mode=1, filter_strength=2
% - 平衡设置：amplitude_mode=2, filter_strength=2
% - 保留细节：amplitude_mode=4, filter_strength=1
% =====================================================

%% ==================== 用户配置区域 ====================
% 1. 数据文件选择
data_filename = 'DanJi_CPML_3D_iso_20250801_133422.mat';

% 2. 要显示的炮点选择（三维数据通常只有一个震源）
shot_numbers = 1; % 三维数据的震源编号

% 3. 数据类型选择
% 1 - 只显示检波器数据
% 2 - 只显示DAS数据
% 3 - 同时显示检波器和DAS数据（分别绘图）
% 4 - 对比显示检波器和DAS数据（同一图中）
data_type = 4;  % 选择数据类型：1=检波器, 2=DAS, 3=分别显示, 4=对比显示

% 4. 检波器/DAS显示配置
config.num_receivers_to_show = 8;  % 每炮显示的检波器/DAS数量（实际数据有13个）
config.receiver_start = 1;          % 起始检波器/DAS编号
% 示例：显示前10个：num_receivers_to_show=10, receiver_start=1
% 示例：显示中间10个：num_receivers_to_show=10, receiver_start=6
% 示例：显示后10个：num_receivers_to_show=10, receiver_start=12

% 5. 声波测井显示参数配置
config.trace_spacing = 1.5;        % 道间距（垂直偏移）
config.amplitude_scale = 0.8;      % 振幅缩放因子
config.show_first_arrivals = false; % 是否标注首波到达
config.time_range = [0, 0.004];    % 显示时间范围[开始, 结束]秒
config.fill_positive = true;       % 是否填充正振幅
config.fill_color = [0.8, 0.2, 0.2]; % 填充颜色 [R, G, B]

% 6. 图片质量配置
config.dpi = 400;                  % 图片分辨率 (300 DPI高清)
config.figure_size = [1200, 900];  % 图形窗口大小 [宽, 高]

% 7. DAS特殊配置
config.das_fill_color = [0.2, 0.2, 0.8]; % DAS数据填充颜色
config.das_amplitude_scale = 0.8;         % DAS振幅缩放因子

% 8. 信号处理配置（减少杂波）
% 滤波强度选择：1=轻微滤波, 2=正常滤波, 3=稍强滤波, 4=强滤波, 0=不滤波
filter_strength = 0;  % 请修改这个数字来选择滤波强度

% 根据滤波强度自动设置参数
switch filter_strength
    case 0  % 不滤波
        config.enable_filtering = false;
        config.enable_smoothing = false;
        fprintf('滤波设置: 不滤波\n');

    case 1  % 轻微滤波
        config.enable_filtering = true;
        config.filter_type = 'lowpass';
        config.lowpass_freq = 3000;              % 较高截止频率，保留更多细节
        config.enable_smoothing = true;
        config.smooth_window = 3;                % 轻微平滑
        fprintf('滤波设置: 轻微滤波 (截止频率: 3000Hz, 平滑窗口: 3)\n');

    case 2  % 正常滤波
        config.enable_filtering = true;
        config.filter_type = 'lowpass';
        config.lowpass_freq = 2000;              % 中等截止频率
        config.enable_smoothing = true;
        config.smooth_window = 5;                % 中等平滑
        fprintf('滤波设置: 正常滤波 (截止频率: 2000Hz, 平滑窗口: 5)\n');

    case 3  % 稍强滤波
        config.enable_filtering = true;
        config.filter_type = 'lowpass';
        config.lowpass_freq = 1500;              % 较低截止频率
        config.enable_smoothing = true;
        config.smooth_window = 7;                % 较强平滑
        fprintf('滤波设置: 稍强滤波 (截止频率: 1500Hz, 平滑窗口: 7)\n');

    case 4  % 强滤波
        config.enable_filtering = true;
        config.filter_type = 'bandpass';
        config.bandpass_freq = [200, 1200];      % 带通滤波，去除低频和高频噪声
        config.enable_smoothing = true;
        config.smooth_window = 9;                % 强平滑
        fprintf('滤波设置: 强滤波 (带通滤波: 200-1200Hz, 平滑窗口: 9)\n');

    otherwise
        warning('无效的filter_strength值，使用默认正常滤波');
        filter_strength = 2;
        config.enable_filtering = true;
        config.filter_type = 'lowpass';
        config.lowpass_freq = 2000;
        config.enable_smoothing = true;
        config.smooth_window = 5;
        fprintf('滤波设置: 正常滤波 (默认)\n');
end

% 备用参数（用于带通滤波）
if ~exist('config.bandpass_freq', 'var')
    config.bandpass_freq = [100, 2000];      % 默认带通滤波频率范围
end

% 振幅显示模式选择（修改amplitude_mode的值）：
% 1 - 传统归一化（所有道振幅相同，清晰显示）
% 2 - 轻微保留振幅差异
% 3 - 中等保留振幅差异
% 4 - 强烈保留振幅差异
% 5 - 不进行归一化，显示原波形
% 6 - 第一道显示原波形，其余道归一化显示
amplitude_mode = 4;  % 请修改这个数字来选择模式

% 模式6专用参数：第一道相对于其他道的振幅倍数（参考例子图片的比例）
first_trace_amplitude_factor = 20.0;

% 根据选择的模式设置参数
switch amplitude_mode
    case 1
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
    case 2
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.3;
    case 3
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.5;
    case 4
        config.preserve_amplitude = true;
        config.amplitude_balance = 0.8;
    case 5
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
        config.no_normalization = true;  % 新增标志：不进行归一化
    case 6
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
        config.first_trace_original = true;  % 新增标志：第一道显示原波形
    otherwise
        warning('无效的amplitude_mode值，使用默认模式1');
        config.preserve_amplitude = false;
        config.amplitude_balance = 0;
end

% 为其他模式设置默认值
if amplitude_mode ~= 5
    config.no_normalization = false;
end
if amplitude_mode ~= 6
    config.first_trace_original = false;
end

%% ==================== 数据加载 ====================
% 加载三维数据文件
current_folder = pwd;
full_data_path = fullfile(current_folder, data_filename);

try
    loaded_data_raw = load(full_data_path);
    fprintf('成功加载三维数据文件: %s\n', full_data_path);

    % 检查数据结构并适配
    if isfield(loaded_data_raw, 'save_data')
        % 新的数据格式，需要转换
        fprintf('检测到新的数据格式，正在转换...\n');
        loaded_data = convert_new_data_format(loaded_data_raw.save_data);
    else
        % 旧的数据格式，直接使用
        loaded_data = loaded_data_raw;
    end

catch ME
    error('数据加载失败: %s', ME.message);
end

% 检查数据类型和可用性
has_receiver_data = isfield(loaded_data, 'receiver_data');
has_das_data = isfield(loaded_data, 'das_data') && isfield(loaded_data, 'enable_das') && loaded_data.enable_das;

fprintf('\n==================== 数据类型检查 ====================\n');
if has_receiver_data
    fprintf('检波器数据: 可用\n');
else
    fprintf('检波器数据: 不可用\n');
end

if has_das_data
    fprintf('DAS数据: 可用\n');
else
    fprintf('DAS数据: 不可用\n');
end

% 根据数据可用性调整显示类型
if data_type == 2 && ~has_das_data
    warning('DAS数据不可用，切换到检波器数据显示');
    data_type = 1;
elseif data_type == 3 && ~has_das_data
    warning('DAS数据不可用，只显示检波器数据');
    data_type = 1;
elseif data_type == 4 && ~has_das_data
    warning('DAS数据不可用，只显示检波器数据');
    data_type = 1;
end

if ~has_receiver_data && ~has_das_data
    error('没有可用的数据进行显示！');
end

% 提取基本参数
if has_receiver_data
    receiver_data = loaded_data.receiver_data;
    num_receivers = loaded_data.num_receivers;
    fprintf('检波器数据尺寸: %d × %d\n', size(receiver_data, 1), size(receiver_data, 2));
end

if has_das_data
    das_data = loaded_data.das_data;
    num_das_points = loaded_data.num_das_points;
    fprintf('DAS数据尺寸: %d × %d\n', size(das_data, 1), size(das_data, 2));
end

% 提取时间轴和其他参数
time_axis = loaded_data.time_axis;
dt = loaded_data.dt;
nt = length(time_axis);

% 提取几何参数
source_x = loaded_data.source_x;
source_y = loaded_data.source_y;
source_z = loaded_data.source_z;

if has_receiver_data
    receiver_x = loaded_data.receiver_x;
    receiver_y = loaded_data.receiver_y;
    receiver_z = loaded_data.receiver_z;
    source_distances = loaded_data.source_distances;
end

% 提取物理参数
dx = loaded_data.dx;
dy = loaded_data.dy;
dz = loaded_data.dz;

fprintf('时间参数: nt=%d, dt=%.2e秒\n', nt, dt);
fprintf('空间参数: dx=dy=dz=%.4f米\n', dx);
fprintf('震源位置: (%.2f, %.2f, %.2f)米\n', source_x, source_y, source_z);

%% ==================== 主处理循环 ====================
% 创建Picture文件夹
picture_dir = fullfile(pwd, 'Picture_3D_AcousticLogging');
if ~exist(picture_dir, 'dir')
    mkdir(picture_dir);
    fprintf('创建Picture文件夹: %s\n', picture_dir);
end

fprintf('\n==================== 开始绘图处理 ====================\n');
switch data_type
    case 1
        fprintf('显示类型: 检波器数据\n');
    case 2
        fprintf('显示类型: DAS数据\n');
    case 3
        fprintf('显示类型: 分别显示\n');
    case 4
        fprintf('显示类型: 对比显示\n');
end

% 确定时间显示范围
if isempty(config.time_range)
    time_indices = 1:nt;
    display_time = time_axis;
else
    time_indices = find(time_axis >= config.time_range(1) & time_axis <= config.time_range(2));
    if isempty(time_indices)
        time_indices = 1:nt;
    end
    display_time = time_axis(time_indices);
end

% 根据数据类型进行绘图
switch data_type
    case 1  % 只显示检波器数据
        if has_receiver_data
            create_3d_acoustic_display(receiver_data, 'receiver', config, loaded_data, ...
                                     display_time, time_indices, first_trace_amplitude_factor);
            save_figure(picture_dir, 'receiver', shot_numbers, config);
        end

    case 2  % 只显示DAS数据
        if has_das_data
            create_3d_acoustic_display(das_data, 'das', config, loaded_data, ...
                                     display_time, time_indices, first_trace_amplitude_factor);
            save_figure(picture_dir, 'das', shot_numbers, config);
        end

    case 3  % 分别显示检波器和DAS数据
        if has_receiver_data
            create_3d_acoustic_display(receiver_data, 'receiver', config, loaded_data, ...
                                     display_time, time_indices, first_trace_amplitude_factor);
            save_figure(picture_dir, 'receiver', shot_numbers, config);
        end
        if has_das_data
            create_3d_acoustic_display(das_data, 'das', config, loaded_data, ...
                                     display_time, time_indices, first_trace_amplitude_factor);
            save_figure(picture_dir, 'das', shot_numbers, config);
        end

    case 4  % 对比显示检波器和DAS数据
        if has_receiver_data && has_das_data
            create_3d_comparison_display(receiver_data, das_data, config, loaded_data, ...
                                       display_time, time_indices, first_trace_amplitude_factor);
            save_figure(picture_dir, 'comparison', shot_numbers, config);
        end
end

fprintf('\n所有图片已保存到: %s\n', picture_dir);
fprintf('程序执行完成！\n');

%% ==================== 辅助函数 ====================
function converted_data = convert_new_data_format(save_data)
    % 将新的数据格式转换为绘图程序期望的格式

    converted_data = struct();

    % 检波器数据
    if isfield(save_data, 'receivers') && isfield(save_data.receivers, 'data')
        converted_data.receiver_data = save_data.receivers.data';  % 转置为时间×检波器格式
        converted_data.num_receivers = save_data.receivers.num_receivers;
        converted_data.enable_das = false;  % 默认值

        % 计算检波器位置信息
        if isfield(save_data.receivers, 'positions')
            positions = save_data.receivers.positions;
            converted_data.receiver_z = positions * save_data.grid.DELTAZ;  % 转换为实际深度
            converted_data.receiver_x = ones(size(positions)) * save_data.source.xsource;
            converted_data.receiver_y = ones(size(positions)) * save_data.source.ysource;

            % 计算源距
            source_z = save_data.source.KSOURCE * save_data.grid.DELTAZ;
            converted_data.source_distances = abs(converted_data.receiver_z - source_z);
        end
    end

    % DAS数据
    if isfield(save_data, 'das') && save_data.das.enabled
        converted_data.das_data = save_data.das.strain_rate_data';  % 转置为时间×DAS点格式
        converted_data.num_das_points = save_data.das.num_das_points;
        converted_data.enable_das = true;
        converted_data.gauge_centers = save_data.das.gauge_centers * save_data.grid.DELTAZ;  % 转换为实际深度
    end

    % 时间轴
    converted_data.time_axis = (0:save_data.time.NSTEP-1) * save_data.time.DELTAT;
    converted_data.dt = save_data.time.DELTAT;

    % 空间参数
    converted_data.dx = save_data.grid.DELTAX;
    converted_data.dy = save_data.grid.DELTAY;
    converted_data.dz = save_data.grid.DELTAZ;

    % 震源位置
    converted_data.source_x = save_data.source.xsource;
    converted_data.source_y = save_data.source.ysource;
    converted_data.source_z = save_data.source.KSOURCE * save_data.grid.DELTAZ;

    % 其他参数
    if isfield(save_data.receivers, 'positions') && length(save_data.receivers.positions) > 1
        converted_data.receiver_spacing = (save_data.receivers.positions(2) - save_data.receivers.positions(1)) * save_data.grid.DELTAZ;
        converted_data.first_receiver_offset = (save_data.receivers.positions(1) - save_data.source.KSOURCE) * save_data.grid.DELTAZ;
    else
        converted_data.receiver_spacing = 0.15;  % 默认值
        converted_data.first_receiver_offset = 3.0;  % 默认值
    end

    fprintf('数据格式转换完成\n');
    if isfield(converted_data, 'receiver_data')
        fprintf('检波器数据: 可用\n');
    else
        fprintf('检波器数据: 不可用\n');
    end

    if isfield(converted_data, 'das_data') && converted_data.enable_das
        fprintf('DAS数据: 可用\n');
    else
        fprintf('DAS数据: 不可用\n');
    end
end
function create_3d_acoustic_display(data_matrix, data_type, config, loaded_data, ...
                                   display_time, time_indices, first_trace_amplitude_factor)
    % 创建三维声波测井标准显示
    % data_matrix: 数据矩阵 (时间 × 检波器/DAS点)
    % data_type: 'receiver' 或 'das'
    % config: 配置参数
    % loaded_data: 加载的完整数据
    % display_time: 显示时间轴
    % time_indices: 时间索引
    % first_trace_amplitude_factor: 第一道振幅因子

    % 创建高质量图形窗口
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'PaperPositionMode', 'auto');
    hold on;

    % 获取数据维度
    [~, num_traces] = size(data_matrix);

    % 计算实际显示的道数范围
    trace_start = config.receiver_start;
    trace_end = min(trace_start + config.num_receivers_to_show - 1, num_traces);
    actual_traces = trace_end - trace_start + 1;

    % 计算归一化因子
    max_amplitude = 0;
    for j = trace_start:trace_end
        temp_data = data_matrix(:, j);
        max_amplitude = max(max_amplitude, max(abs(temp_data)));
    end
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end

    % 选择振幅缩放和填充颜色
    if strcmp(data_type, 'das')
        amplitude_scale = config.das_amplitude_scale;
        fill_color = config.das_fill_color;
    else
        amplitude_scale = config.amplitude_scale;
        fill_color = config.fill_color;
    end

    % 绘制每道数据
    first_arrivals = zeros(1, actual_traces);
    first_arrival_count = 0;

    for i = trace_start:trace_end
        % 道偏移位置（从1开始连续编号）
        display_index = i - trace_start + 1;

        % 为模式6的第一道留出更多空间
        if config.first_trace_original && display_index == 1
            trace_offset = display_index * config.trace_spacing * 4.0;
        elseif config.first_trace_original && display_index > 1
            trace_offset = config.trace_spacing * 4.0 + (display_index - 1) * config.trace_spacing;
        else
            trace_offset = display_index * config.trace_spacing;
        end

        % 提取数据
        orig_data = data_matrix(:, i);

        % 检查时间索引是否超出范围
        if max(time_indices) > length(orig_data)
            warning('时间索引超出范围，使用全部时间数据');
            time_indices = 1:length(orig_data);
            display_time = loaded_data.time_axis(time_indices);
        end

        plot_data = orig_data(time_indices);

        % 信号处理（滤波和平滑）
        if config.enable_filtering || config.enable_smoothing
            plot_data = apply_signal_processing(plot_data, config, loaded_data);
        end

        % 智能振幅处理
        if config.no_normalization
            % 模式5：不进行归一化，显示原波形
            norm_data = plot_data * amplitude_scale;
        elseif config.first_trace_original
            % 模式6：第一道显示原波形，其余道归一化显示
            if i == trace_start
                % 第一道：显示原波形，但进行适当缩放
                first_trace_max = max(abs(plot_data));
                if first_trace_max > 0
                    first_trace_scale = amplitude_scale * first_trace_amplitude_factor;
                    norm_data = plot_data / first_trace_max * first_trace_scale;
                else
                    norm_data = plot_data * amplitude_scale;
                end
            else
                % 其余道：传统归一化
                norm_data = plot_data / max_amplitude * amplitude_scale;
            end
        elseif config.preserve_amplitude
            % 计算该道的最大振幅
            trace_max = max(abs(plot_data));

            if trace_max > 0
                % 混合归一化策略
                normalized_part = plot_data / max_amplitude * amplitude_scale;
                preserved_part = plot_data / trace_max * amplitude_scale * (trace_max / max_amplitude);
                norm_data = config.amplitude_balance * preserved_part + ...
                           (1 - config.amplitude_balance) * normalized_part;
            else
                norm_data = plot_data * amplitude_scale;
            end
        else
            % 传统归一化
            norm_data = plot_data / max_amplitude * amplitude_scale;
        end

        % 绘制基线
        plot([display_time(1), display_time(end)], [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);

        % 绘制波形
        wave_y = norm_data + trace_offset;
        plot(display_time, wave_y, 'k-', 'LineWidth', 1.2);

        % 填充正振幅（如果启用）
        if config.fill_positive
            positive_idx = norm_data > 0;
            if any(positive_idx)
                % 找到连续的正振幅区间
                positive_idx = positive_idx(:);
                diff_idx = diff([false; positive_idx; false]);
                start_idx = find(diff_idx == 1);
                end_idx = find(diff_idx == -1) - 1;

                % 对每个连续区间分别填充
                for seg = 1:length(start_idx)
                    seg_start = start_idx(seg);
                    seg_end = end_idx(seg);

                    seg_time = display_time(seg_start:seg_end);
                    seg_wave = wave_y(seg_start:seg_end);
                    seg_baseline = trace_offset * ones(size(seg_time));

                    % 确保所有数组都是行向量
                    seg_time = seg_time(:)';
                    seg_wave = seg_wave(:)';
                    seg_baseline = seg_baseline(:)';

                    fill_x = [seg_time, fliplr(seg_time)];
                    fill_y = [seg_wave, fliplr(seg_baseline)];
                    fill(fill_x, fill_y, fill_color, 'EdgeColor', 'none', 'FaceAlpha', 0.6);
                end
            end
        end

        % 检测首波到达（如果启用）
        if config.show_first_arrivals
            first_arrival = detect_first_arrival_simple(plot_data, display_time);
            if first_arrival > 0
                plot(first_arrival, trace_offset, 'ro', 'MarkerSize', 6, ...
                     'MarkerFaceColor', 'r', 'MarkerEdgeColor', 'k', 'LineWidth', 1);
                first_arrival_count = first_arrival_count + 1;
                first_arrivals(first_arrival_count) = first_arrival;
            end
        end
    end

    % 设置图形属性
    xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');

    % 根据数据类型设置标题，包含第一道的实际深度
    first_trace_num = trace_start;
    if strcmp(data_type, 'das') && isfield(loaded_data, 'gauge_centers')
        first_depth = loaded_data.gauge_centers(first_trace_num);
    elseif isfield(loaded_data, 'receiver_z')
        first_depth = loaded_data.receiver_z(first_trace_num);
    else
        first_depth = loaded_data.source_z + loaded_data.first_receiver_offset;
    end

    if strcmp(data_type, 'das')
        title_str = sprintf('三维DAS声波测井数据 (震源: %.2fm, 第1道: %.2fm)', ...
                           loaded_data.source_z, first_depth);
    else
        title_str = sprintf('三维检波器声波测井数据 (震源: %.2fm, 第1道: %.2fm)', ...
                           loaded_data.source_z, first_depth);
    end
    title(title_str, 'FontSize', 16, 'FontWeight', 'bold');

    % 网格和轴设置
    grid on;
    set(gca, 'LineWidth', 1.2, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 11);

    % 计算Y轴刻度位置，需要与波形绘制位置匹配
    y_positions = zeros(1, actual_traces);
    depth_labels = cell(1, actual_traces);

    for i = 1:actual_traces
        actual_trace_num = trace_start + i - 1;
        display_index = i;  % 对应波形绘制时的display_index

        % 计算波形的实际Y位置（与绘制逻辑保持一致）
        if config.first_trace_original && display_index == 1
            y_positions(i) = display_index * config.trace_spacing * 4.0;
        elseif config.first_trace_original && display_index > 1
            y_positions(i) = config.trace_spacing * 4.0 + (display_index - 1) * config.trace_spacing;
        else
            y_positions(i) = display_index * config.trace_spacing;
        end

        % 根据数据类型获取深度信息
        if strcmp(data_type, 'das') && isfield(loaded_data, 'gauge_centers')
            absolute_depth = loaded_data.gauge_centers(actual_trace_num);
        elseif isfield(loaded_data, 'receiver_z')
            absolute_depth = loaded_data.receiver_z(actual_trace_num);
        else
            % 如果没有具体深度信息，使用估算值
            absolute_depth = loaded_data.source_z + loaded_data.first_receiver_offset + ...
                           (actual_trace_num-1) * loaded_data.receiver_spacing;
        end

        depth_labels{i} = sprintf('%.2f', absolute_depth);
    end

    % Y轴设置 - 使用与波形绘制位置匹配的Y位置
    yticks(y_positions);
    yticklabels(depth_labels);

    % 范围设置
    xlim([display_time(1), display_time(end)]);

    % Y轴范围基于实际波形位置
    if config.first_trace_original
        y_bottom = y_positions(1) - 5.0 * config.trace_spacing;
        y_top = y_positions(end) + 0.5 * config.trace_spacing;
        ylim([y_bottom, y_top]);
    else
        y_bottom = y_positions(1) - 0.5 * config.trace_spacing;
        y_top = y_positions(end) + 0.5 * config.trace_spacing;
        ylim([y_bottom, y_top]);
    end
end

function create_3d_comparison_display(receiver_data, das_data, config, loaded_data, ...
                                    display_time, time_indices, first_trace_amplitude_factor)
    % 创建检波器和DAS数据叠加对比显示（类似您提供的图片样式）

    % 创建高质量图形窗口（白色背景）
    figure('Position', [100, 100, config.figure_size], 'Color', 'w', ...
           'PaperPositionMode', 'auto');
    hold on;

    % 获取数据维度
    [~, num_traces] = size(receiver_data);

    % 计算实际显示的道数范围
    trace_start = config.receiver_start;
    trace_end = min(trace_start + config.num_receivers_to_show - 1, num_traces);
    actual_traces = trace_end - trace_start + 1;

    % 分别计算检波器和DAS数据的归一化因子
    max_amplitude_receiver = 0;
    max_amplitude_das = 0;

    for j = trace_start:trace_end
        temp_receiver = receiver_data(:, j);
        temp_das = das_data(:, j);
        max_amplitude_receiver = max(max_amplitude_receiver, max(abs(temp_receiver)));
        max_amplitude_das = max(max_amplitude_das, max(abs(temp_das)));
    end

    if max_amplitude_receiver < 1e-10, max_amplitude_receiver = 1; end
    if max_amplitude_das < 1e-10, max_amplitude_das = 1; end

    % 绘制每道的叠加数据
    for i = trace_start:trace_end
        display_index = i - trace_start + 1;
        trace_offset = display_index * config.trace_spacing;

        % 提取检波器和DAS数据
        receiver_orig = receiver_data(:, i);
        das_orig = das_data(:, i);

        % 检查时间索引
        if max(time_indices) > length(receiver_orig)
            warning('时间索引超出范围，使用全部时间数据');
            time_indices = 1:length(receiver_orig);
            display_time = loaded_data.time_axis(time_indices);
        end

        receiver_plot = receiver_orig(time_indices);
        das_plot = das_orig(time_indices);

        % 信号处理（滤波和平滑）
        if config.enable_filtering || config.enable_smoothing
            receiver_plot = apply_signal_processing(receiver_plot, config, loaded_data);
            das_plot = apply_signal_processing(das_plot, config, loaded_data);
        end

        % 归一化处理（使用统一的振幅缩放）
        receiver_norm = receiver_plot / max_amplitude_receiver * config.amplitude_scale;
        das_norm = das_plot / max_amplitude_das * config.das_amplitude_scale;

        % 绘制基线（灰色）
        plot([display_time(1), display_time(end)], [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.5, 'Color', [0.7, 0.7, 0.7]);

        % 绘制检波器波形（黑色，加粗）
        receiver_wave_y = receiver_norm + trace_offset;
        plot(display_time, receiver_wave_y, 'k-', 'LineWidth', 2.5);

        % 绘制DAS波形（浅红色，细线）
        das_wave_y = das_norm + trace_offset;
        plot(display_time, das_wave_y, '-', 'Color', [0.8, 0.3, 0.3], 'LineWidth', 0.8);

        % 不填充正振幅区域（去掉填充效果）
    end

    % 设置图形属性（黑色文字，白色背景）
    xlabel('时间 (秒)', 'FontSize', 14, 'FontWeight', 'bold');
    ylabel('深度 (米)', 'FontSize', 14, 'FontWeight', 'bold');

    title_str = sprintf('第%d炮检波器/DAS对比 (深度: %.2fm)', 1, loaded_data.source_z);
    title(title_str, 'FontSize', 16, 'FontWeight', 'bold');

    % 添加图例（检波器黑色粗线，DAS浅红色细线）
    legend({'检波器 (黑色)', 'DAS (浅红色)'}, 'Location', 'northeast', 'FontSize', 12, ...
           'Box', 'on');

    % 网格和轴设置（正常的灰色网格）
    grid on;
    set(gca, 'LineWidth', 1.2, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 11);

    % 计算Y轴位置和深度标签
    y_positions = zeros(1, actual_traces);
    depth_labels = cell(1, actual_traces);
    for i = 1:actual_traces
        actual_trace_num = trace_start + i - 1;
        y_positions(i) = i * config.trace_spacing;  % 波形在图上的Y位置

        if isfield(loaded_data, 'receiver_z')
            depth = loaded_data.receiver_z(actual_trace_num);
        else
            depth = loaded_data.source_z + loaded_data.first_receiver_offset + ...
                   (actual_trace_num-1) * loaded_data.receiver_spacing;
        end

        depth_labels{i} = sprintf('%.2f', depth);
    end

    % Y轴设置
    yticks(y_positions);
    yticklabels(depth_labels);
    xlim([display_time(1), display_time(end)]);

    % Y轴范围基于实际波形位置
    y_bottom = y_positions(1) - 0.5 * config.trace_spacing;
    y_top = y_positions(end) + 0.5 * config.trace_spacing;
    ylim([y_bottom, y_top]);
end

function create_subplot_display(data_matrix, data_type, config, loaded_data, ...
                               display_time, time_indices, ~)
    % 创建子图显示（用于对比模式）

    hold on;

    % 获取数据维度
    [~, num_traces] = size(data_matrix);

    % 计算实际显示的道数范围
    trace_start = config.receiver_start;
    trace_end = min(trace_start + config.num_receivers_to_show - 1, num_traces);
    actual_traces = trace_end - trace_start + 1;

    % 计算归一化因子
    max_amplitude = 0;
    for j = trace_start:trace_end
        temp_data = data_matrix(:, j);
        max_amplitude = max(max_amplitude, max(abs(temp_data)));
    end
    if max_amplitude < 1e-10
        max_amplitude = 1;
    end

    % 选择振幅缩放和填充颜色
    if strcmp(data_type, 'das')
        amplitude_scale = config.das_amplitude_scale;
        fill_color = config.das_fill_color;
    else
        amplitude_scale = config.amplitude_scale;
        fill_color = config.fill_color;
    end

    % 绘制每道数据（简化版本，适合子图）
    for i = trace_start:trace_end
        display_index = i - trace_start + 1;
        trace_offset = display_index * config.trace_spacing;

        orig_data = data_matrix(:, i);
        plot_data = orig_data(time_indices);

        % 信号处理（滤波和平滑）
        if config.enable_filtering || config.enable_smoothing
            plot_data = apply_signal_processing(plot_data, config, loaded_data);
        end

        % 简化的振幅处理（对比模式使用统一归一化）
        norm_data = plot_data / max_amplitude * amplitude_scale;

        % 绘制基线和波形
        plot([display_time(1), display_time(end)], [trace_offset, trace_offset], ...
             'k-', 'LineWidth', 0.3, 'Color', [0.7, 0.7, 0.7]);

        wave_y = norm_data + trace_offset;
        plot(display_time, wave_y, 'k-', 'LineWidth', 1.0);

        % 填充正振幅
        if config.fill_positive
            positive_idx = norm_data > 0;
            if any(positive_idx)
                positive_idx = positive_idx(:);
                diff_idx = diff([false; positive_idx; false]);
                start_idx = find(diff_idx == 1);
                end_idx = find(diff_idx == -1) - 1;

                for seg = 1:length(start_idx)
                    seg_start = start_idx(seg);
                    seg_end = end_idx(seg);

                    seg_time = display_time(seg_start:seg_end);
                    seg_wave = wave_y(seg_start:seg_end);
                    seg_baseline = trace_offset * ones(size(seg_time));

                    % 确保所有数组都是行向量
                    seg_time = seg_time(:)';
                    seg_wave = seg_wave(:)';
                    seg_baseline = seg_baseline(:)';

                    fill_x = [seg_time, fliplr(seg_time)];
                    fill_y = [seg_wave, fliplr(seg_baseline)];
                    fill(fill_x, fill_y, fill_color, 'EdgeColor', 'none', 'FaceAlpha', 0.5);
                end
            end
        end
    end

    % 设置子图属性
    xlabel('时间 (秒)', 'FontSize', 12, 'FontWeight', 'bold');
    ylabel('深度 (米)', 'FontSize', 12, 'FontWeight', 'bold');

    if strcmp(data_type, 'das')
        title('DAS数据', 'FontSize', 14, 'FontWeight', 'bold');
    else
        title('检波器数据', 'FontSize', 14, 'FontWeight', 'bold');
    end

    grid on;
    set(gca, 'LineWidth', 1.0, 'GridLineStyle', ':', 'GridAlpha', 0.3);
    set(gca, 'FontSize', 10);

    % 计算Y轴位置和深度标签
    y_positions = zeros(1, actual_traces);
    depth_labels = cell(1, actual_traces);
    for i = 1:actual_traces
        actual_trace_num = trace_start + i - 1;
        y_positions(i) = i * config.trace_spacing;  % 波形在图上的Y位置

        if strcmp(data_type, 'das') && isfield(loaded_data, 'gauge_centers')
            depth = loaded_data.gauge_centers(actual_trace_num);
        elseif isfield(loaded_data, 'receiver_z')
            depth = loaded_data.receiver_z(actual_trace_num);
        else
            depth = loaded_data.source_z + loaded_data.first_receiver_offset + ...
                   (actual_trace_num-1) * loaded_data.receiver_spacing;
        end

        depth_labels{i} = sprintf('%.2f', depth);
    end

    % Y轴设置
    yticks(y_positions);
    yticklabels(depth_labels);
    xlim([display_time(1), display_time(end)]);

    % Y轴范围基于实际波形位置
    y_bottom = y_positions(1) - 0.5 * config.trace_spacing;
    y_top = y_positions(end) + 0.5 * config.trace_spacing;
    ylim([y_bottom, y_top]);
end

function save_figure(picture_dir, data_type, shot_numbers, config)
    % 保存图形文件

    if strcmp(data_type, 'comparison')
        fig_filename = fullfile(picture_dir, sprintf('三维声波测井_对比_炮点_%d.png', shot_numbers));
    else
        fig_filename = fullfile(picture_dir, sprintf('三维声波测井_%s_炮点_%d.png', data_type, shot_numbers));
    end

    % 设置高分辨率保存参数
    set(gcf, 'PaperPositionMode', 'auto');
    set(gcf, 'PaperUnits', 'inches');
    set(gcf, 'PaperSize', [12, 9]);

    % 使用print函数保存高质量图片
    print(gcf, fig_filename, '-dpng', sprintf('-r%d', config.dpi));
    fprintf('保存高清图片 (%d DPI): %s\n', config.dpi, fig_filename);

    % 关闭图形窗口
    close(gcf);
end

function fill_positive_regions(display_time, wave_y, trace_offset, positive_idx, fill_color, alpha)
    % 填充正振幅区域的辅助函数
    positive_idx = positive_idx(:);
    diff_idx = diff([false; positive_idx; false]);
    start_idx = find(diff_idx == 1);
    end_idx = find(diff_idx == -1) - 1;

    for seg = 1:length(start_idx)
        seg_start = start_idx(seg);
        seg_end = end_idx(seg);

        seg_time = display_time(seg_start:seg_end);
        seg_wave = wave_y(seg_start:seg_end);
        seg_baseline = trace_offset * ones(size(seg_time));

        % 确保所有数组都是行向量
        seg_time = seg_time(:)';
        seg_wave = seg_wave(:)';
        seg_baseline = seg_baseline(:)';

        fill_x = [seg_time, fliplr(seg_time)];
        fill_y = [seg_wave, fliplr(seg_baseline)];
        fill(fill_x, fill_y, fill_color, 'EdgeColor', 'none', 'FaceAlpha', alpha);
    end
end

function first_arrival_time = detect_first_arrival_simple(data, time_vec)
    % 简单的首波到达检测
    abs_data = abs(data);
    threshold = 0.05 * max(abs_data);
    first_idx = find(abs_data > threshold, 1, 'first');

    if ~isempty(first_idx) && first_idx > 1
        first_arrival_time = time_vec(first_idx);
    else
        first_arrival_time = -1;
    end
end

function processed_data = apply_signal_processing(data, config, loaded_data)
    % 应用信号处理（滤波和平滑）来减少杂波

    processed_data = data;

    % 获取采样频率
    if isfield(loaded_data, 'dt')
        dt = loaded_data.dt;
    else
        dt = 1e-5;  % 默认采样间隔
    end
    fs = 1/dt;  % 采样频率

    % 滤波处理
    if config.enable_filtering && ~strcmp(config.filter_type, 'none')
        try
            switch config.filter_type
                case 'lowpass'
                    % 低通滤波
                    if config.lowpass_freq < fs/2
                        [b, a] = butter(4, config.lowpass_freq/(fs/2), 'low');
                        processed_data = filtfilt(b, a, processed_data);
                    end

                case 'bandpass'
                    % 带通滤波
                    low_freq = config.bandpass_freq(1);
                    high_freq = config.bandpass_freq(2);
                    if low_freq > 0 && high_freq < fs/2 && low_freq < high_freq
                        [b, a] = butter(4, [low_freq, high_freq]/(fs/2), 'bandpass');
                        processed_data = filtfilt(b, a, processed_data);
                    end
            end
        catch ME
            fprintf('滤波处理失败: %s\n', ME.message);
        end
    end

    % 平滑处理
    if config.enable_smoothing && config.smooth_window > 1
        try
            % 使用移动平均平滑
            window_size = config.smooth_window;
            if mod(window_size, 2) == 0
                window_size = window_size + 1;  % 确保窗口大小为奇数
            end

            if window_size <= length(processed_data)
                processed_data = smooth(processed_data, window_size, 'moving');
            end
        catch ME
            fprintf('平滑处理失败: %s\n', ME.message);
        end
    end
end
