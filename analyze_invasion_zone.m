% 分析main.m程序中的侵入带模型
% 可视化侵入带的几何结构和物理参数

clear; clc;

fprintf('=== main.m程序中的侵入带模型分析 ===\n');

% 从main.m中提取的关键参数
% 基础参数
vp1 = 1500;        % 井孔纵波速度 [m/s]
f0 = 10*10^3;      % 主频率 [Hz]
cal = 0.1;         % 井径 [m]

% 网格参数
la1 = vp1/f0;      % 波长计算 [m]
dx = la1/10;       % X方向空间步长 [m]
dz = la1/10;       % Z方向空间步长 [m]
nx = 300;          % X方向网格数
nz = 1300;         % Z方向网格数

% 井孔位置
med_x = fix(nx/2) - fix(nx/4);  % 井轴在X方向的位置
l_cal = ceil(cal/dx)/1;         % 井径对应的网格点数

% 介质物理参数
% 井孔介质参数（泥浆）
po1 = 1.0*10^3;    % 密度 [kg/m^3]
vp1 = 1500;        % 纵波速度 [m/s]
vs1 = 0;           % 横波速度 [m/s] （流体）

% 地层介质参数（围岩）
po2 = 2.3*10^3;    % 密度 [kg/m^3]
vp2 = 4500;        % 纵波速度 [m/s]
vs2 = 2300;        % 横波速度 [m/s]

% 侵入带参数
Formation_D = 1.0;             % 侵入带径向厚度 [m]
Formation_DIter = ceil(Formation_D/dz);  % 侵入带径向网格数
Formation_H = 1.0;             % 侵入带轴向高度 [m]
Formation_HIter = ceil(Formation_H/dz);  % 侵入带轴向网格数

% 侵入带介质参数
Vpout = 2300;  % 侵入带纵波速度 [m/s]
Vsout = 1300;  % 侵入带横波速度 [m/s]
Denout = 1800; % 侵入带密度 [kg/m^3]

fprintf('\n=== 基础参数 ===\n');
fprintf('网格参数:\n');
fprintf('  dx = dz = %.4f m\n', dx);
fprintf('  nx = %d, nz = %d\n', nx, nz);
fprintf('  总模型尺寸: %.2f × %.2f m\n', nx*dx, nz*dz);

fprintf('\n井孔参数:\n');
fprintf('  井径: %.2f m\n', cal);
fprintf('  井轴位置: 第%d列 (%.3f m)\n', med_x, med_x*dx);
fprintf('  井径网格数: %d 点\n', l_cal);

fprintf('\n=== 侵入带几何参数 ===\n');
fprintf('径向参数:\n');
fprintf('  侵入带径向厚度: %.2f m\n', Formation_D);
fprintf('  侵入带径向网格数: %d 点\n', Formation_DIter);
fprintf('  侵入带径向范围: 第%d列 到 第%d列\n', med_x+l_cal+1, med_x+l_cal+Formation_DIter);

fprintf('\n轴向参数:\n');
fprintf('  侵入带轴向高度: %.2f m\n', Formation_H);
fprintf('  侵入带轴向网格数: %d 点\n', Formation_HIter);
fprintf('  侵入带轴向范围: 第%d行 到 第%d行\n', ...
    nz/2-ceil(Formation_HIter/2), nz/2+ceil(Formation_HIter/2));

fprintf('\n=== 介质物理参数对比 ===\n');
fprintf('参数类型\t\t密度(kg/m³)\t纵波速度(m/s)\t横波速度(m/s)\n');
fprintf('井孔(泥浆)\t\t%d\t\t%d\t\t%d\n', po1, vp1, vs1);
fprintf('地层(围岩)\t\t%d\t\t%d\t\t%d\n', po2, vp2, vs2);
fprintf('侵入带\t\t\t%d\t\t%d\t\t%d\n', Denout, Vpout, Vsout);

% 构建速度模型进行可视化
vp = ones(nz, nx) * vp2;  % 初始化为地层速度
vs = ones(nz, nx) * vs2;
dens = ones(nz, nx) * po2;

% 基本地质模型构建（井孔+围岩）
for count_j = 1:nx
    if count_j < med_x - l_cal      % 左侧地层区域
        vp(:, count_j) = vp2;
        vs(:, count_j) = vs2;
        dens(:, count_j) = po2;
    elseif count_j >= med_x - l_cal && count_j <= med_x + l_cal  % 井孔区域
        vp(:, count_j) = vp1;
        vs(:, count_j) = vs1;
        dens(:, count_j) = po1;
    elseif count_j > med_x + l_cal  % 右侧地层区域
        vp(:, count_j) = vp2;
        vs(:, count_j) = vs2;
        dens(:, count_j) = po2;
    end
end

% 侵入带区域赋值
for count_i = nz/2-ceil(Formation_HIter/2):nz/2+ceil(Formation_HIter/2)
    for count_j = med_x+l_cal+1:med_x+l_cal+Formation_DIter
        vp(count_i, count_j) = Vpout;
        vs(count_j, count_j) = Vsout;
        dens(count_i, count_j) = Denout;
    end
end

fprintf('\n=== 侵入带物理意义 ===\n');
fprintf('侵入带模拟的是钻井过程中泥浆滤液侵入地层形成的改变带:\n');
fprintf('1. 位置: 紧邻井壁的地层区域\n');
fprintf('2. 成因: 钻井泥浆滤液侵入原始地层\n');
fprintf('3. 特征: 物性介于泥浆和原始地层之间\n');
fprintf('4. 影响: 改变声波传播特性，影响测井响应\n');

% 可视化模型
fprintf('\n=== 生成可视化图像 ===\n');

% 创建X和Z坐标轴
x_coords = (1:nx) * dx;
z_coords = (1:nz) * dz;

% 创建图形窗口
figure('Position', [100, 100, 1400, 1000], 'Color', 'w');

% 子图1: 纵波速度分布
subplot(2, 3, 1);
imagesc(x_coords, z_coords, vp);
colorbar;
title('纵波速度分布 (m/s)', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
ylabel('Z方向距离 (m)');
colormap(gca, 'jet');

% 标注关键区域
hold on;
% 井孔边界
plot([med_x-l_cal, med_x-l_cal]*dx, [0, nz*dz], 'w--', 'LineWidth', 2);
plot([med_x+l_cal, med_x+l_cal]*dx, [0, nz*dz], 'w--', 'LineWidth', 2);
% 侵入带边界
invasion_start_x = (med_x + l_cal + 1) * dx;
invasion_end_x = (med_x + l_cal + Formation_DIter) * dx;
invasion_start_z = (nz/2 - ceil(Formation_HIter/2)) * dz;
invasion_end_z = (nz/2 + ceil(Formation_HIter/2)) * dz;
rectangle('Position', [invasion_start_x, invasion_start_z, ...
    invasion_end_x-invasion_start_x, invasion_end_z-invasion_start_z], ...
    'EdgeColor', 'white', 'LineWidth', 2, 'LineStyle', '--');
text(invasion_start_x + 0.2, invasion_start_z + 0.2, '侵入带', ...
    'Color', 'white', 'FontSize', 10, 'FontWeight', 'bold');

% 子图2: 横波速度分布
subplot(2, 3, 2);
imagesc(x_coords, z_coords, vs);
colorbar;
title('横波速度分布 (m/s)', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
ylabel('Z方向距离 (m)');
colormap(gca, 'jet');

% 子图3: 密度分布
subplot(2, 3, 3);
imagesc(x_coords, z_coords, dens);
colorbar;
title('密度分布 (kg/m³)', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
ylabel('Z方向距离 (m)');
colormap(gca, 'jet');

% 子图4: 模型结构示意图
subplot(2, 3, 4);
% 创建简化的结构图
structure_model = ones(nz, nx);
% 井孔区域 = 1
for j = med_x-l_cal:med_x+l_cal
    structure_model(:, j) = 1;
end
% 地层区域 = 2
structure_model(:, 1:med_x-l_cal-1) = 2;
structure_model(:, med_x+l_cal+1:end) = 2;
% 侵入带区域 = 3
for i = nz/2-ceil(Formation_HIter/2):nz/2+ceil(Formation_HIter/2)
    for j = med_x+l_cal+1:med_x+l_cal+Formation_DIter
        structure_model(i, j) = 3;
    end
end

imagesc(x_coords, z_coords, structure_model);
colormap(gca, [0.6 0.8 1.0; 0.8 0.6 0.4; 1.0 0.6 0.6]);  % 蓝色井孔，棕色地层，红色侵入带
title('模型结构示意图', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
ylabel('Z方向距离 (m)');

% 添加图例
legend_labels = {'井孔(泥浆)', '地层(围岩)', '侵入带'};
colorbar('Ticks', [1, 2, 3], 'TickLabels', legend_labels);

% 子图5: 径向剖面图（通过井轴中心）
subplot(2, 3, 5);
center_z = round(nz/2);
radial_vp = vp(center_z, :);
radial_vs = vs(center_z, :);
radial_dens = dens(center_z, :);

yyaxis left;
plot(x_coords, radial_vp, 'b-', 'LineWidth', 2, 'DisplayName', '纵波速度');
hold on;
plot(x_coords, radial_vs, 'r-', 'LineWidth', 2, 'DisplayName', '横波速度');
ylabel('速度 (m/s)', 'Color', 'k');
ylim([0, 5000]);

yyaxis right;
plot(x_coords, radial_dens, 'g-', 'LineWidth', 2, 'DisplayName', '密度');
ylabel('密度 (kg/m³)', 'Color', 'k');

title('径向物性剖面 (通过井轴中心)', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('X方向距离 (m)');
legend('纵波速度', '横波速度', '密度', 'Location', 'best');
grid on;

% 标注区域边界
xline((med_x-l_cal)*dx, '--k', '井孔左边界');
xline((med_x+l_cal)*dx, '--k', '井孔右边界');
xline((med_x+l_cal+1)*dx, '--r', '侵入带开始');
xline((med_x+l_cal+Formation_DIter)*dx, '--r', '侵入带结束');

% 子图6: 轴向剖面图（通过侵入带中心）
subplot(2, 3, 6);
invasion_center_x = round(med_x + l_cal + Formation_DIter/2);
axial_vp = vp(:, invasion_center_x);
axial_vs = vs(:, invasion_center_x);
axial_dens = dens(:, invasion_center_x);

yyaxis left;
plot(z_coords, axial_vp, 'b-', 'LineWidth', 2, 'DisplayName', '纵波速度');
hold on;
plot(z_coords, axial_vs, 'r-', 'LineWidth', 2, 'DisplayName', '横波速度');
ylabel('速度 (m/s)', 'Color', 'k');
ylim([0, 5000]);

yyaxis right;
plot(z_coords, axial_dens, 'g-', 'LineWidth', 2, 'DisplayName', '密度');
ylabel('密度 (kg/m³)', 'Color', 'k');

title('轴向物性剖面 (通过侵入带中心)', 'FontSize', 12, 'FontWeight', 'bold');
xlabel('Z方向距离 (m)');
legend('纵波速度', '横波速度', '密度', 'Location', 'best');
grid on;

% 标注侵入带边界
xline(invasion_start_z, '--r', '侵入带上边界');
xline(invasion_end_z, '--r', '侵入带下边界');

sgtitle('main.m程序中的侵入带模型分析', 'FontSize', 16, 'FontWeight', 'bold');

fprintf('可视化图像生成完成！\n');
fprintf('\n=== 总结 ===\n');
fprintf('main.m程序实现了一个包含侵入带的三维声波测井模型:\n');
fprintf('1. 几何结构: 井孔 + 侵入带 + 围岩地层\n');
fprintf('2. 侵入带位置: 紧邻井壁右侧，厚度%.2fm，高度%.2fm\n', Formation_D, Formation_H);
fprintf('3. 物性特征: 介于泥浆和地层之间的过渡性质\n');
fprintf('4. 实际意义: 模拟钻井泥浆滤液对地层的侵入影响\n');
fprintf('5. 测井影响: 会改变声波传播路径和到达时间\n');
